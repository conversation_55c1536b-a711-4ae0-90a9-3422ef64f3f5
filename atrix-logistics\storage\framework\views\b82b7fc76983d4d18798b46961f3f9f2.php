<?php $__env->startSection('title', 'Newsletter Subscribers'); ?>

<?php $__env->startSection('page-header'); ?>
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Newsletter Subscribers</h1>
            <p class="text-muted">Manage your newsletter subscriber list</p>
        </div>
        <div>
            <a href="<?php echo e(route('admin.communications.newsletter.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Subscriber
            </a>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Subscribers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['total']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['active']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Unsubscribed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['unsubscribed']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-times fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-danger">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Bounced</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['bounced']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.communications.newsletter.index')); ?>">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">All Statuses</option>
                                <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                                <option value="unsubscribed" <?php echo e(request('status') === 'unsubscribed' ? 'selected' : ''); ?>>Unsubscribed</option>
                                <option value="bounced" <?php echo e(request('status') === 'bounced' ? 'selected' : ''); ?>>Bounced</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by email or name..." 
                                   value="<?php echo e(request('search')); ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="<?php echo e(route('admin.communications.newsletter.index')); ?>" class="btn btn-secondary">Clear</a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Import Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-upload me-2"></i>
                Bulk Import Subscribers
            </h6>
        </div>
        <div class="card-body">
            <form method="POST" action="<?php echo e(route('admin.communications.newsletter.import')); ?>">
                <?php echo csrf_field(); ?>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label for="emails">Email Addresses (one per line)</label>
                            <textarea name="emails" id="emails" class="form-control" rows="4" 
                                      placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"></textarea>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-upload me-2"></i>
                                    Import Subscribers
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Subscribers Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Newsletter Subscribers</h6>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleBulkActions()">
                        <i class="fas fa-tasks"></i> Bulk Actions
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if($subscribers->count() > 0): ?>
                <!-- Bulk Actions (hidden by default) -->
                <div id="bulk-actions" class="mb-3" style="display: none;">
                    <form method="POST" action="<?php echo e(route('admin.communications.newsletter.bulk-action')); ?>" id="bulk-form">
                        <?php echo csrf_field(); ?>
                        <div class="row align-items-end">
                            <div class="col-md-4">
                                <select name="action" class="form-control" required>
                                    <option value="">Select Action</option>
                                    <option value="activate">Activate</option>
                                    <option value="unsubscribe">Unsubscribe</option>
                                    <option value="delete">Delete</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Apply to Selected</button>
                                <button type="button" class="btn btn-secondary" onclick="toggleBulkActions()">Cancel</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th width="30">
                                    <input type="checkbox" id="select-all">
                                </th>
                                <th>Email</th>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Subscribed</th>
                                <th>Source</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $subscribers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscriber): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" name="subscribers[]" value="<?php echo e($subscriber->id); ?>" class="subscriber-checkbox">
                                    </td>
                                    <td><?php echo e($subscriber->email); ?></td>
                                    <td><?php echo e($subscriber->name ?: '-'); ?></td>
                                    <td>
                                        <span class="text-dark badge <?php echo e($subscriber->status_badge); ?>">
                                            <?php echo e(ucfirst($subscriber->status)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php echo e($subscriber->subscribed_at->format('M j, Y')); ?>

                                        <br><small class="text-muted"><?php echo e($subscriber->subscribed_at->format('g:i A')); ?></small>
                                    </td>
                                    <td>
                                        <span class="text-dark badge badge-light"><?php echo e(ucfirst(str_replace('_', ' ', $subscriber->subscription_source))); ?></span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.communications.newsletter.show', $subscriber)); ?>" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.communications.newsletter.edit', $subscriber)); ?>" 
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?php echo e(route('admin.communications.newsletter.destroy', $subscriber)); ?>" 
                                                  style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div>
                    <?php echo e($subscribers->appends(request()->query())->links('pagination.admin')); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-newspaper fa-3x text-gray-300 mb-3"></i>
                    <h5>No newsletter subscribers found</h5>
                    <p class="text-muted">Newsletter subscribers will appear here when visitors subscribe to your newsletter.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function toggleBulkActions() {
    const bulkActions = document.getElementById('bulk-actions');
    bulkActions.style.display = bulkActions.style.display === 'none' ? 'block' : 'none';
}

// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.subscriber-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Bulk form submission
document.getElementById('bulk-form').addEventListener('submit', function(e) {
    const selectedSubscribers = document.querySelectorAll('.subscriber-checkbox:checked');
    if (selectedSubscribers.length === 0) {
        e.preventDefault();
        alert('Please select at least one subscriber.');
        return;
    }
    
    const action = this.querySelector('select[name="action"]').value;
    if (action === 'delete') {
        if (!confirm('Are you sure you want to delete the selected subscribers?')) {
            e.preventDefault();
        }
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/admin/communications/newsletter/index.blade.php ENDPATH**/ ?>