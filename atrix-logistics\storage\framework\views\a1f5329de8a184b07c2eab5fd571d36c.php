<?php $__env->startSection('title', 'Categories'); ?>
<?php $__env->startSection('page-title', 'Product Categories'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.categories.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Add Category
        </a>
        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-cog me-1"></i> Actions
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="toggleSortMode()">
                <i class="fas fa-sort me-2"></i> Reorder Categories
            </a></li>
            <li><a class="dropdown-item" href="<?php echo e(route('admin.categories.tree')); ?>">
                <i class="fas fa-sitemap me-2"></i> Category Tree
            </a></li>
        </ul>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($categories->total()); ?></h4>
                            <p class="mb-0">Total Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-folder fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($categories->where('is_active', true)->count()); ?></h4>
                            <p class="mb-0">Active Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($categories->where('parent_id', null)->count()); ?></h4>
                            <p class="mb-0">Root Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($categories->sum('products_count')); ?></h4>
                            <p class="mb-0">Total Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-box fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-folder me-2"></i>
                    Categories
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="expandAll()">
                        <i class="fas fa-expand-arrows-alt me-1"></i> Expand All
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAll()">
                        <i class="fas fa-compress-arrows-alt me-1"></i> Collapse All
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if($categories->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="categoriesTable">
                        <thead>
                            <tr>
                                <th width="40%">Category</th>
                                <th width="15%">Products</th>
                                <th width="10%">Status</th>
                                <th width="10%">Featured</th>
                                <th width="10%">Order</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="sortable-categories">
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr data-id="<?php echo e($category->id); ?>" class="category-row">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($category->children->count() > 0): ?>
                                                <button class="btn btn-sm btn-link p-0 me-2 toggle-children" 
                                                        data-category="<?php echo e($category->id); ?>">
                                                    <i class="fas fa-chevron-right"></i>
                                                </button>
                                            <?php else: ?>
                                                <span class="me-4"></span>
                                            <?php endif; ?>
                                            
                                            <?php if($category->image): ?>
                                                <img src="<?php echo e(Storage::url($category->image)); ?>" 
                                                     alt="<?php echo e($category->name); ?>" 
                                                     class="rounded me-2" 
                                                     style="width: 32px; height: 32px; object-fit: cover;">
                                            <?php elseif($category->icon): ?>
                                                <i class="<?php echo e($category->icon); ?> fa-lg me-2 text-primary"></i>
                                            <?php else: ?>
                                                <i class="fas fa-folder fa-lg me-2 text-muted"></i>
                                            <?php endif; ?>
                                            
                                            <div>
                                                <strong><?php echo e($category->name); ?></strong>
                                                <?php if($category->description): ?>
                                                    <br><small class="text-muted"><?php echo e(Str::limit($category->description, 50)); ?></small>
                                                <?php endif; ?>
                                                <br><small class="text-info"><?php echo e($category->slug); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo e($category->products_count); ?></span>
                                        <?php if($category->products_count > 0): ?>
                                            <a href="<?php echo e(route('admin.products.index', ['category' => $category->id])); ?>" 
                                               class="btn btn-sm btn-link p-0 ms-1">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <form method="POST" action="/admin/ecommerce/categories/<?php echo e($category->id); ?>/toggle-status" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                <?php if($category->is_active): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </button>
                                        </form>
                                    </td>
                                    <td>
                                        <?php if($category->is_featured): ?>
                                            <span class="badge bg-warning">Featured</span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="text-muted"><?php echo e($category->sort_order); ?></span>
                                        <i class="fas fa-grip-vertical text-muted ms-2 sort-handle" style="cursor: move;"></i>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.categories.show', $category)); ?>" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.categories.edit', $category)); ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteCategory(<?php echo e($category->id); ?>, '<?php echo e($category->name); ?>')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                
                                <?php $__currentLoopData = $category->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr data-id="<?php echo e($child->id); ?>" class="category-row child-category" 
                                        data-parent="<?php echo e($category->id); ?>" style="display: none;">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2 ms-4">└─</span>
                                                
                                                <?php if($child->image): ?>
                                                    <img src="<?php echo e(Storage::url($child->image)); ?>" 
                                                         alt="<?php echo e($child->name); ?>" 
                                                         class="rounded me-2" 
                                                         style="width: 24px; height: 24px; object-fit: cover;">
                                                <?php elseif($child->icon): ?>
                                                    <i class="<?php echo e($child->icon); ?> me-2 text-primary"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-folder me-2 text-muted"></i>
                                                <?php endif; ?>
                                                
                                                <div>
                                                    <strong><?php echo e($child->name); ?></strong>
                                                    <?php if($child->description): ?>
                                                        <br><small class="text-muted"><?php echo e(Str::limit($child->description, 40)); ?></small>
                                                    <?php endif; ?>
                                                    <br><small class="text-info"><?php echo e($child->slug); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($child->products_count); ?></span>
                                        </td>
                                        <td>
                                            <?php if($child->is_active): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($child->is_featured): ?>
                                                <span class="badge bg-warning">Featured</span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="text-muted"><?php echo e($child->sort_order); ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.categories.show', $child)); ?>" 
                                                   class="btn btn-sm btn-outline-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.categories.edit', $child)); ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteCategory(<?php echo e($child->id); ?>, '<?php echo e($child->name); ?>')" 
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-4">
                    <?php echo e($categories->links('pagination.admin')); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No categories found</h5>
                    <p class="text-muted">Start by creating your first product category.</p>
                    <a href="<?php echo e(route('admin.categories.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Create First Category
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Delete Category
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the category <strong id="deleteCategoryName"></strong>?</p>
                    <p class="text-muted">This action cannot be undone. All products in this category will need to be reassigned.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" id="deleteForm" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>
                            Delete Category
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
    // Toggle child categories
    document.querySelectorAll('.toggle-children').forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.dataset.category;
            const childRows = document.querySelectorAll(`[data-parent="${categoryId}"]`);
            const icon = this.querySelector('i');
            
            childRows.forEach(row => {
                if (row.style.display === 'none') {
                    row.style.display = '';
                    icon.classList.remove('fa-chevron-right');
                    icon.classList.add('fa-chevron-down');
                } else {
                    row.style.display = 'none';
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-right');
                }
            });
        });
    });

    // Expand all categories
    function expandAll() {
        document.querySelectorAll('.child-category').forEach(row => {
            row.style.display = '';
        });
        document.querySelectorAll('.toggle-children i').forEach(icon => {
            icon.classList.remove('fa-chevron-right');
            icon.classList.add('fa-chevron-down');
        });
    }

    // Collapse all categories
    function collapseAll() {
        document.querySelectorAll('.child-category').forEach(row => {
            row.style.display = 'none';
        });
        document.querySelectorAll('.toggle-children i').forEach(icon => {
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-right');
        });
    }

    // Delete category
    function deleteCategory(categoryId, categoryName) {
        document.getElementById('deleteCategoryName').textContent = categoryName;
        document.getElementById('deleteForm').action = `/admin/ecommerce/categories/${categoryId}`;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    // Sortable functionality
    let sortable;
    function toggleSortMode() {
        if (sortable) {
            sortable.destroy();
            sortable = null;
            document.querySelectorAll('.sort-handle').forEach(handle => {
                handle.style.display = 'none';
            });
        } else {
            sortable = Sortable.create(document.getElementById('sortable-categories'), {
                handle: '.sort-handle',
                animation: 150,
                onEnd: function(evt) {
                    const orders = [];
                    document.querySelectorAll('.category-row').forEach((row, index) => {
                        orders.push(row.dataset.id);
                    });
                    
                    // Send AJAX request to update order
                    fetch('/admin/ecommerce/categories/update-order', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({ orders: orders })
                    });
                }
            });
            
            document.querySelectorAll('.sort-handle').forEach(handle => {
                handle.style.display = 'inline';
            });
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/admin/categories/index.blade.php ENDPATH**/ ?>