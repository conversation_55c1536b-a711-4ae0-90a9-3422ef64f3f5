<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\Order;
use App\Models\UserAddress;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

class CheckoutController extends Controller
{

    /**
     * Show checkout page
     */
    public function index()
    {
        $cart = Cart::getCurrent();
        
        if ($cart->isEmpty()) {
            return redirect()->route('cart.index')->with('error', 'Your cart is empty.');
        }

        $cart->load(['items.product']);
        $user = Auth::user();
        $addresses = $user->addresses()->get();
        $defaultShipping = $addresses->where('type', 'shipping')->where('is_default', true)->first();
        $defaultBilling = $addresses->where('type', 'billing')->where('is_default', true)->first();

        return view('frontend.checkout.index', compact('cart', 'addresses', 'defaultShipping', 'defaultBilling'));
    }

    /**
     * Process checkout
     */
    public function process(Request $request): JsonResponse
    {
        // Ensure we're authenticated
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please log in to place an order.',
            ], 401);
        }
        try {
            $request->validate([
                'shipping_address_id' => 'required|exists:user_addresses,id',
                'billing_address_id' => 'required|exists:user_addresses,id',
                'payment_method' => 'required|in:manual,paypal,stripe',
                'notes' => 'nullable|string|max:1000',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422);
        }

        $cart = Cart::getCurrent();
        
        if ($cart->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Your cart is empty.',
            ], 400);
        }

        $user = Auth::user();
        
        // Verify addresses belong to user
        $shippingAddress = $user->addresses()->findOrFail($request->shipping_address_id);
        $billingAddress = $user->addresses()->findOrFail($request->billing_address_id);

        try {
            DB::beginTransaction();

            // Create order (order_number will be auto-generated)
            $order = Order::create([
                'customer_id' => $user->id,
                'customer_name' => $user->name,
                'customer_email' => $user->email,
                'customer_phone' => $user->phone,
                'status' => 'pending',
                'payment_status' => 'pending',
                'payment_method' => $request->payment_method,
                'subtotal' => $cart->subtotal,
                'tax_amount' => $cart->tax_amount,
                'shipping_amount' => $cart->shipping_amount,
                'discount_amount' => $cart->discount_amount,
                'total_amount' => $cart->total_amount,
                'notes' => $request->notes,
                // Billing address fields
                'billing_first_name' => $billingAddress->first_name,
                'billing_last_name' => $billingAddress->last_name,
                'billing_company' => $billingAddress->company,
                'billing_address_1' => $billingAddress->address_line_1,
                'billing_address_2' => $billingAddress->address_line_2,
                'billing_city' => $billingAddress->city,
                'billing_state' => $billingAddress->state,
                'billing_postal_code' => $billingAddress->postal_code,
                'billing_country' => $billingAddress->country,
                // Shipping address fields
                'shipping_first_name' => $shippingAddress->first_name,
                'shipping_last_name' => $shippingAddress->last_name,
                'shipping_company' => $shippingAddress->company,
                'shipping_address_1' => $shippingAddress->address_line_1,
                'shipping_address_2' => $shippingAddress->address_line_2,
                'shipping_city' => $shippingAddress->city,
                'shipping_state' => $shippingAddress->state,
                'shipping_postal_code' => $shippingAddress->postal_code,
                'shipping_country' => $shippingAddress->country,
            ]);

            // Create order items
            foreach ($cart->items as $cartItem) {
                $order->items()->create([
                    'product_id' => $cartItem->product_id,
                    'quantity' => $cartItem->quantity,
                    'unit_price' => $cartItem->unit_price,
                    'total_price' => $cartItem->total_price,
                    'product_name' => $cartItem->product->name,
                    'product_sku' => $cartItem->product->sku,
                    'product_attributes' => $cartItem->product_options,
                ]);

                // Update stock if managed
                if ($cartItem->product->manage_stock) {
                    $cartItem->product->decrement('stock_quantity', $cartItem->quantity);
                }
            }

            // Clear cart
            $cart->clear();

            DB::commit();

            // Handle payment based on method
            $paymentResponse = $this->handlePayment($order, $request->payment_method);

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully.',
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_response' => $paymentResponse,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            // Log the error for debugging
            \Log::error('Checkout process failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process order. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Handle payment processing
     */
    private function handlePayment(Order $order, string $paymentMethod): array
    {
        switch ($paymentMethod) {
            case 'manual':
                return [
                    'type' => 'manual',
                    'message' => 'Order placed successfully. Payment instructions will be sent via email.',
                    'redirect' => route('checkout.confirmation', $order),
                ];

            case 'paypal':
                // Integrate with PayPal (similar to existing implementation)
                return [
                    'type' => 'paypal',
                    'message' => 'Redirecting to PayPal...',
                    'redirect' => route('customer.orders.pay', $order),
                ];

            case 'stripe':
                // Integrate with Stripe (similar to existing implementation)
                return [
                    'type' => 'stripe',
                    'message' => 'Processing payment...',
                    'redirect' => route('customer.orders.pay', $order),
                ];

            default:
                throw new \Exception('Invalid payment method');
        }
    }



    /**
     * Show order confirmation
     */
    public function confirmation(Order $order): View
    {
        if ($order->customer_id !== Auth::id()) {
            abort(404);
        }

        $order->load(['items.product']);

        return view('frontend.checkout.confirmation', compact('order'));
    }
}
