<?php
    $seoService = app(\App\Services\SeoLocalizationService::class);
    $currentLocale = $seoService->getCurrentLocale();
?>

<?php $__env->startSection('title', $seoService->getLocalizedMetaTitle($currentLocale, $category->name . ' - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics'))); ?>
<?php $__env->startSection('description', $seoService->getLocalizedMetaDescription($currentLocale, $category->description ?? 'Browse our ' . $category->name . ' products with professional logistics support and worldwide shipping.')); ?>
<?php $__env->startSection('keywords', $category->name . ', ' . strtolower($category->name) . ', logistics, shipping, freight, ' . ($siteSettings['site_keywords'] ?? 'automotive parts, steel products, containers')); ?>

<?php $__env->startSection('content'); ?>

<!-- Hero Section -->
<?php if (isset($component)) { $__componentOriginala9d931d4f11b4d2850df99e991db1dca = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala9d931d4f11b4d2850df99e991db1dca = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-hero','data' => ['title' => ''.e($category->name).'','subtitle' => ''.e($category->description ?? 'Quality products with professional logistics support').'','description' => 'Browse our comprehensive range of '.e(strtolower($category->name)).' with professional logistics support.','breadcrumbs' => [
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Products', 'url' => route('products.index')],
        ['title' => $category->name]
    ],'gradient' => 'from-blue-900 via-green-800 to-gray-900']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => ''.e($category->name).'','subtitle' => ''.e($category->description ?? 'Quality products with professional logistics support').'','description' => 'Browse our comprehensive range of '.e(strtolower($category->name)).' with professional logistics support.','breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Products', 'url' => route('products.index')],
        ['title' => $category->name]
    ]),'gradient' => 'from-blue-900 via-green-800 to-gray-900']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $attributes = $__attributesOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $component = $__componentOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__componentOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>

<!-- Search Section -->
<section class="py-12 bg-white border-b border-gray-200">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto">
            <form method="GET" class="flex gap-4">
                <input type="text" name="search" 
                       class="flex-1 px-6 py-4 rounded-xl border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" 
                       placeholder="Search in <?php echo e($category->name); ?>..." 
                       value="<?php echo e(request('search')); ?>">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-xl font-semibold transition-colors shadow-lg hover:shadow-xl">
                    <i class="fas fa-search mr-2"></i>Search
                </button>
            </form>
        </div>
    </div>
</section>

<!-- Products Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Filters -->
            <div class="lg:w-1/4">
                <div class="bg-white rounded-2xl shadow-lg p-6 sticky top-8">
                    <h3 class="text-lg font-bold text-gray-900 mb-6">
                        <i class="fas fa-filter mr-2 text-green-600"></i>Filters
                    </h3>
                    
                    <form method="GET" class="space-y-6">
                        <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                        
                        <!-- Subcategories -->
                        <?php if($subcategories->count() > 0): ?>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Subcategories</h4>
                            <div class="space-y-2">
                                <?php $__currentLoopData = $subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <label class="flex items-center">
                                    <input type="checkbox" name="subcategory[]" value="<?php echo e($subcategory->slug); ?>" 
                                           <?php echo e(in_array($subcategory->slug, request('subcategory', [])) ? 'checked' : ''); ?>

                                           class="text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <span class="ml-2 text-gray-700"><?php echo e($subcategory->name); ?></span>
                                </label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Price Range -->
                        <?php if($priceRange && $priceRange->min_price && $priceRange->max_price): ?>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Price Range</h4>
                            <div class="grid grid-cols-2 gap-2">
                                <input type="number" name="min_price" 
                                       class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500" 
                                       placeholder="Min" value="<?php echo e(request('min_price')); ?>" 
                                       min="<?php echo e($priceRange->min_price); ?>" max="<?php echo e($priceRange->max_price); ?>">
                                <input type="number" name="max_price" 
                                       class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500" 
                                       placeholder="Max" value="<?php echo e(request('max_price')); ?>" 
                                       min="<?php echo e($priceRange->min_price); ?>" max="<?php echo e($priceRange->max_price); ?>">
                            </div>
                            <p class="text-sm text-gray-500 mt-2">
                                Range: <?php echo \App\Helpers\CurrencyHelper::format($priceRange->min_price); ?> - <?php echo \App\Helpers\CurrencyHelper::format($priceRange->max_price); ?>
                            </p>
                        </div>
                        <?php endif; ?>
                        
                        <div class="space-y-3">
                            <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors">
                                Apply Filters
                            </button>
                            <a href="<?php echo e(route('categories.show', $category)); ?>" class="block w-full bg-gray-100 hover:bg-gray-200 text-gray-800 py-3 rounded-lg font-semibold transition-colors text-center">
                                Clear All
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="lg:w-3/4">
                <!-- Sort Options -->
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900"><?php echo e($category->name); ?></h2>
                        <p class="text-gray-600"><?php echo e($products->total()); ?> products found</p>
                    </div>
                    <div class="flex items-center gap-4">
                        <form method="GET" class="flex items-center gap-2">
                            <?php $__currentLoopData = request()->except('sort'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <input type="hidden" name="<?php echo e($key); ?>" value="<?php echo e($value); ?>">
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <label class="text-sm text-gray-600">Sort by:</label>
                            <select name="sort" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500" onchange="this.form.submit()">
                                <option value="newest" <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Newest First</option>
                                <option value="oldest" <?php echo e(request('sort') == 'oldest' ? 'selected' : ''); ?>>Oldest First</option>
                                <option value="name" <?php echo e(request('sort') == 'name' ? 'selected' : ''); ?>>Name A-Z</option>
                                <option value="price_low" <?php echo e(request('sort') == 'price_low' ? 'selected' : ''); ?>>Price: Low to High</option>
                                <option value="price_high" <?php echo e(request('sort') == 'price_high' ? 'selected' : ''); ?>>Price: High to Low</option>
                            </select>
                        </form>
                    </div>
                </div>

                <!-- Products -->
                <?php if($products->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 card-hover animate-on-scroll overflow-hidden">
                        <div class="relative">
                            <img src="<?php echo e($product->featured_image_url ?? 'https://via.placeholder.com/400x300?text=No+Image'); ?>" 
                                 alt="<?php echo e($product->name); ?>" class="w-full h-64 object-cover">
                            
                            <?php if($product->isOnSale()): ?>
                            <span class="absolute top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                <?php echo e($product->discount_percentage); ?>% OFF
                            </span>
                            <?php endif; ?>
                            
                            <?php if($product->shouldShowPrice()): ?>
                                <div class="absolute top-3 right-3 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full">
                                    <?php if($product->isOnSale()): ?>
                                        <span class="line-through text-sm"><?php echo \App\Helpers\CurrencyHelper::format($product->price); ?></span>
                                        <span class="font-bold ml-1"><?php echo \App\Helpers\CurrencyHelper::format($product->sale_price); ?></span>
                                    <?php else: ?>
                                        <span class="font-bold"><?php echo \App\Helpers\CurrencyHelper::format($product->price); ?></span>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="absolute top-3 right-3 bg-green-600 text-white px-3 py-1 rounded-full">
                                    <span class="font-bold text-sm">Request Quote</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="p-6">
                            <div class="mb-4">
                                <?php if($product->category): ?>
                                <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mb-2">
                                    <?php echo e($product->category->name); ?>

                                </span>
                                <?php endif; ?>
                                <h3 class="text-xl font-bold text-gray-900 mb-2"><?php echo e($product->name); ?></h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    <?php echo e(Str::limit($product->short_description, 120)); ?>

                                </p>
                            </div>
                            
                            <div class="flex gap-2">
                                <a href="<?php echo e(route('products.show', $product)); ?>"
                                   class="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded-lg text-sm font-medium transition-colors text-center">
                                    <i class="fas fa-eye mr-1"></i>View
                                </a>

                                <?php if($product->shouldShowPrice()): ?>
                                    <button onclick="addToCart('<?php echo e($product->id); ?>', '<?php echo e($product->name); ?>', '<?php echo e($product->getCurrentPrice()); ?>')"
                                            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-shopping-cart mr-1"></i>Cart
                                    </button>
                                    <button onclick="openProductQuote('<?php echo e($product->id); ?>', '<?php echo e($product->name); ?>', '<?php echo e($product->getCurrentPrice()); ?>')"
                                            class="bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-plus mr-1"></i>Quote
                                    </button>
                                <?php else: ?>
                                    <button onclick="openProductQuote('<?php echo e($product->id); ?>', '<?php echo e($product->name); ?>', '<?php echo e($product->price); ?>')"
                                            class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-plus mr-1"></i>Quote
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-12 flex justify-center">
                    <?php echo e($products->appends(request()->query())->links()); ?>

                </div>
                <?php else: ?>
                <div class="text-center py-20">
                    <div class="max-w-md mx-auto">
                        <i class="fas fa-box-open text-6xl text-gray-400 mb-6"></i>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">No products found</h3>
                        <p class="text-gray-600 mb-6">No products found in <?php echo e($category->name); ?>. Try adjusting your search or filters.</p>
                        <a href="<?php echo e(route('categories.show', $category)); ?>" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                            View All <?php echo e($category->name); ?>

                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('head'); ?>
<!-- Category-specific Open Graph Tags -->
<meta property="og:type" content="website">
<meta property="og:title" content="<?php echo e($category->name); ?> - <?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>">
<meta property="og:description" content="<?php echo e($category->description ?? 'Browse our ' . $category->name . ' products with professional logistics support.'); ?>">
<meta property="og:url" content="<?php echo e(route('categories.show', $category)); ?>">
<?php if($category->image): ?>
<meta property="og:image" content="<?php echo e(Storage::url($category->image)); ?>">
<?php endif; ?>

<!-- Twitter Card Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="<?php echo e($category->name); ?> - <?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>">
<meta name="twitter:description" content="<?php echo e($category->description ?? 'Browse our ' . $category->name . ' products with professional logistics support.'); ?>">

<!-- Canonical URL -->
<link rel="canonical" href="<?php echo e(route('categories.show', $category)); ?>">

<!-- Structured Data for Category -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "<?php echo e($category->name); ?>",
    "description": "<?php echo e($category->description ?? 'Browse our ' . $category->name . ' products with professional logistics support.'); ?>",
    "url": "<?php echo e(route('categories.show', $category)); ?>",
    "mainEntity": {
        "@type": "ItemList",
        "name": "<?php echo e($category->name); ?> Products",
        "numberOfItems": <?php echo e($products->total()); ?>,
        "itemListElement": [
            <?php $__currentLoopData = $products->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            {
                "@type": "ListItem",
                "position": <?php echo e($index + 1); ?>,
                "item": {
                    "@type": "Product",
                    "name": "<?php echo e($product->name); ?>",
                    "description": "<?php echo e($product->short_description ?? $product->name); ?>",
                    "url": "<?php echo e(route('products.show', $product)); ?>",
                    <?php if($product->featured_image_url): ?>
                    "image": "<?php echo e($product->featured_image_url); ?>",
                    <?php endif; ?>
                    "offers": {
                        "@type": "Offer",
                        "price": "<?php echo e($product->isOnSale() ? $product->sale_price : $product->price); ?>",
                        "priceCurrency": "<?php echo e($seoService->getCurrencyForLocale($currentLocale)); ?>",
                        "availability": "<?php echo e($product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'); ?>",
                        "seller": {
                            "@type": "Organization",
                            "name": "<?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>"
                        }
                    }
                }
            }<?php echo e(!$loop->last ? ',' : ''); ?>

            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        ]
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "<?php echo e(route('home')); ?>"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Products",
                "item": "<?php echo e(route('products.index')); ?>"
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": "<?php echo e($category->name); ?>"
            }
        ]
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('components.quote-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function openProductQuote(productId, productName, productPrice) {
    // This function is handled by the quote modal component
    if (typeof window.openProductQuote === 'function') {
        window.openProductQuote(productId, productName, productPrice);
    } else {
        // Fallback - switch to product tab and show modal
        const productTab = document.getElementById('product-tab');
        if (productTab) {
            productTab.click();
        }

        // Show modal
        openQuoteModal();

        // Add product to quote if functions are available
        if (typeof addProductToQuote === 'function') {
            addProductToQuote(productId, productName, productPrice, 1);
        }
    }
}

function addToCart(productId, productName, productPrice) {
    fetch('/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Update cart count if function exists
            if (typeof updateCartCount === 'function') {
                updateCartCount();
            }
        } else {
            if (data.redirect) {
                // Redirect to login if not authenticated
                window.location.href = data.redirect;
            } else {
                showNotification(data.message, 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/frontend/categories/show.blade.php ENDPATH**/ ?>