@extends('layouts.frontend')

@section('title', 'Products - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics'))
@section('description', 'Browse our comprehensive range of automotive parts, shipping containers, and steel products with professional logistics support.')

@section('content')

<!-- Hero Section -->
<x-page-hero
    title="Our <span class='text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400'>Products</span>"
    subtitle="{{ $siteSettings['products_hero_subtitle'] ?? 'Quality products with professional logistics support' }}"
    description="Browse our comprehensive range of automotive parts, shipping containers, and steel products."
    :breadcrumbs="[
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Products']
    ]"
    gradient="from-gray-900 via-green-800 to-blue-900"
/>

<!-- Search Section -->
<section class="py-12 bg-white border-b border-gray-200">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto">
            <form method="GET" class="flex gap-4">
                <input type="text" name="search"
                       class="flex-1 px-6 py-4 rounded-xl border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                       placeholder="Search products..."
                       value="{{ request('search') }}">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-xl font-semibold transition-colors shadow-lg hover:shadow-xl">
                    <i class="fas fa-search mr-2"></i>Search
                </button>
            </form>
        </div>
    </div>
</section>

<!-- Products Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Filters Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-gray-50 rounded-2xl p-6 sticky top-24">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">
                        <i class="fas fa-filter mr-2 text-green-600"></i>Filters
                    </h3>
                    
                    <form method="GET" id="filterForm" class="space-y-6">
                        <input type="hidden" name="search" value="{{ request('search') }}">
                        
                        <!-- Categories -->
                        @if($categories->count() > 0)
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Categories</h4>
                            <div class="space-y-2">
                                @foreach($categories as $category)
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="{{ $category->slug }}" 
                                           {{ request('category') == $category->slug ? 'checked' : '' }}
                                           class="text-green-600 focus:ring-green-500 border-gray-300">
                                    <span class="ml-2 text-gray-700">{{ $category->name }}</span>
                                </label>
                                @endforeach
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="" 
                                           {{ !request('category') ? 'checked' : '' }}
                                           class="text-green-600 focus:ring-green-500 border-gray-300">
                                    <span class="ml-2 text-gray-700">All Categories</span>
                                </label>
                            </div>
                        </div>
                        @endif
                        
                        <!-- Price Range -->
                        @if($priceRange && $priceRange->min_price && $priceRange->max_price)
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Price Range</h4>
                            <div class="grid grid-cols-2 gap-2">
                                <input type="number" name="min_price" 
                                       class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500" 
                                       placeholder="Min" value="{{ request('min_price') }}" 
                                       min="{{ $priceRange->min_price }}" max="{{ $priceRange->max_price }}">
                                <input type="number" name="max_price" 
                                       class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500" 
                                       placeholder="Max" value="{{ request('max_price') }}" 
                                       min="{{ $priceRange->min_price }}" max="{{ $priceRange->max_price }}">
                            </div>
                            <p class="text-sm text-gray-500 mt-2">
                                Range: @currency($priceRange->min_price) - @currency($priceRange->max_price)
                            </p>
                        </div>
                        @endif
                        
                        <div class="space-y-3">
                            <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors">
                                Apply Filters
                            </button>
                            <a href="{{ route('products.index') }}" class="block w-full bg-gray-200 hover:bg-gray-300 text-gray-800 py-3 rounded-lg font-semibold transition-colors text-center">
                                Clear All
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="lg:col-span-3">
                <!-- Sort Options -->
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Products</h2>
                        <p class="text-gray-600">{{ $products->total() }} products found</p>
                    </div>
                    <div class="flex items-center gap-4">
                        <form method="GET" class="flex items-center gap-2">
                            @foreach(request()->except('sort') as $key => $value)
                                <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                            @endforeach
                            <label class="text-sm text-gray-600">Sort by:</label>
                            <select name="sort" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500" onchange="this.form.submit()">
                                <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest First</option>
                                <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                                <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Name A-Z</option>
                                <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                                <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                            </select>
                        </form>
                        
                        <button onclick="openQuoteModal()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors">
                            <i class="fas fa-quote-left mr-2"></i>Get Quote
                        </button>
                    </div>
                </div>

                <!-- Products -->
                @if($products->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                    @foreach($products as $product)
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 card-hover animate-on-scroll overflow-hidden">
                        <div class="relative">
                            <img src="{{ $product->featured_image_url ?? 'https://via.placeholder.com/400x300?text=No+Image' }}" 
                                 alt="{{ $product->name }}" class="w-full h-64 object-cover">
                            
                            @if($product->isOnSale())
                            <span class="absolute top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                {{ $product->discount_percentage }}% OFF
                            </span>
                            @endif
                            
                            @if($product->shouldShowPrice())
                                <div class="absolute top-3 right-3 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full">
                                    @if($product->isOnSale())
                                        <span class="line-through text-sm">@currency($product->price)</span>
                                        <span class="font-bold ml-1">@currency($product->sale_price)</span>
                                    @else
                                        <span class="font-bold">@currency($product->price)</span>
                                    @endif
                                </div>
                            @else
                                <div class="absolute top-3 right-3 bg-green-600 text-white px-3 py-1 rounded-full">
                                    <span class="font-bold text-sm">Request Quote</span>
                                </div>
                            @endif
                        </div>
                        
                        <div class="p-6">
                            <div class="mb-4">
                                @if($product->category)
                                <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mb-2">
                                    {{ $product->category->name }}
                                </span>
                                @endif
                                <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $product->name }}</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    {{ Str::limit($product->short_description, 120) }}
                                </p>
                            </div>
                            
                            <div class="flex gap-2">
                                <a href="{{ route('products.show', $product) }}"
                                   class="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded-lg text-sm font-medium transition-colors text-center">
                                    <i class="fas fa-eye mr-1"></i>View
                                </a>

                                @if($product->shouldShowPrice())
                                    <button onclick="addToCart('{{ $product->id }}', '{{ $product->name }}', '{{ $product->getCurrentPrice() }}')"
                                            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-shopping-cart mr-1"></i>Cart
                                    </button>
                                    <button onclick="openProductQuote('{{ $product->id }}', '{{ $product->name }}', '{{ $product->getCurrentPrice() }}')"
                                            class="bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-plus mr-1"></i>Quote
                                    </button>
                                @else
                                    <button onclick="openProductQuote('{{ $product->id }}', '{{ $product->name }}', '{{ $product->price }}')"
                                            class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-plus mr-1"></i>Quote
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12 flex justify-center">
                    {{ $products->appends(request()->query())->links() }}
                </div>
                @else
                <div class="text-center py-20">
                    <div class="max-w-md mx-auto">
                        <i class="fas fa-box-open text-6xl text-gray-400 mb-6"></i>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">No products found</h3>
                        <p class="text-gray-600 mb-6">Try adjusting your search criteria or browse all products.</p>
                        <a href="{{ route('products.index') }}" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                            View All Products
                        </a>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Quote Modal -->
@include('components.quote-modal')

<script>
// openProductQuote function is provided by the quote modal component

function addToCart(productId, productName, productPrice) {
    fetch('/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Update cart count if function exists
            if (typeof updateCartCount === 'function') {
                updateCartCount();
            }
        } else {
            if (data.redirect) {
                // Redirect to login if not authenticated
                window.location.href = data.redirect;
            } else {
                showNotification(data.message, 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}
</script>
@endsection
