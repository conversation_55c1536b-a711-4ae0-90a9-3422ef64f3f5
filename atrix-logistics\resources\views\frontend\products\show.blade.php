@extends('layouts.frontend')

@php
    $seoService = app(\App\Services\SeoLocalizationService::class);
    $currentLocale = $seoService->getCurrentLocale();
    $currentCurrency = $seoService->getCurrencyForLocale($currentLocale);
@endphp

@section('title', $seoService->getLocalizedMetaTitle($currentLocale, $product->name . ' - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics')))
@section('description', $seoService->getLocalizedMetaDescription($currentLocale, $product->short_description ?? $product->name . ' - Quality product with professional logistics support and worldwide shipping.'))
@section('keywords', $product->name . ', ' . ($product->category ? $product->category->name . ', ' : '') . 'logistics, shipping, freight, ' . ($siteSettings['site_keywords'] ?? 'automotive parts, steel products, containers'))

@section('content')
<!-- Breadcrumb -->
<section class="bg-gray-50 py-6">
    <div class="container mx-auto px-4">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('home') }}" class="text-gray-600 hover:text-green-600 transition-colors">
                        <i class="fas fa-home mr-2"></i>Home
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <a href="{{ route('products.index') }}" class="text-gray-600 hover:text-green-600 transition-colors">Products</a>
                    </div>
                </li>
                @if($product->category)
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="text-gray-600">{{ $product->category->name }}</span>
                    </div>
                </li>
                @endif
                <li aria-current="page">
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="text-gray-900 font-medium">{{ $product->name }}</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>
</section>

<!-- Product Details -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Product Images -->
            <div class="space-y-4">
                <div class="relative">
                    <img src="{{ $product->featured_image_url ?? 'https://via.placeholder.com/600x400?text=No+Image' }}" 
                         alt="{{ $product->name }}" 
                         class="w-full h-96 object-cover rounded-2xl shadow-lg">
                    
                    @if($product->isOnSale())
                    <span class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full font-semibold">
                        {{ $product->discount_percentage }}% OFF
                    </span>
                    @endif
                </div>
                
                @if(!empty($product->gallery_image_urls))
                <div class="grid grid-cols-4 gap-2">
                    @foreach(array_slice($product->gallery_image_urls, 0, 4) as $image)
                    <img src="{{ $image }}" alt="{{ $product->name }}" 
                         class="w-full h-20 object-cover rounded-lg cursor-pointer hover:opacity-75 transition-opacity">
                    @endforeach
                </div>
                @endif
            </div>

            <!-- Product Information -->
            <div class="space-y-6">
                @if($product->category)
                <span class="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                    {{ $product->category->name }}
                </span>
                @endif
                
                <h1 class="text-3xl lg:text-4xl font-bold text-gray-900">{{ $product->name }}</h1>
                
                <!-- Price -->
                <div class="flex items-center space-x-4">
                    @if($product->isOnSale())
                        <span class="text-3xl font-bold text-green-600">@currency($product->sale_price)</span>
                        <span class="text-xl text-gray-500 line-through">@currency($product->price)</span>
                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium">
                            Save {{ $product->discount_percentage }}%
                        </span>
                    @else
                        <span class="text-3xl font-bold text-green-600">@currency($product->price)</span>
                    @endif
                </div>
                
                <!-- Stock Status -->
                <div class="flex items-center space-x-2">
                    @if($product->isInStock())
                        <i class="fas fa-check-circle text-green-500"></i>
                        <span class="text-green-600 font-medium">{{ $product->stock_status_text }}</span>
                    @else
                        <i class="fas fa-times-circle text-red-500"></i>
                        <span class="text-red-600 font-medium">{{ $product->stock_status_text }}</span>
                    @endif
                </div>
                
                <!-- Short Description -->
                @if($product->short_description)
                <p class="text-lg text-gray-600 leading-relaxed">{{ $product->short_description }}</p>
                @endif
                
                <!-- Product Details -->
                @if($product->sku)
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">SKU:</span>
                            <span class="font-medium text-gray-900">{{ $product->sku }}</span>
                        </div>
                        @if($product->weight)
                        <div>
                            <span class="text-gray-500">Weight:</span>
                            <span class="font-medium text-gray-900">{{ $product->weight }} kg</span>
                        </div>
                        @endif
                    </div>
                </div>
                @endif
                
                <!-- Action Buttons -->
                <div class="space-y-4">
                    @if($product->hasPrice())
                        <!-- Add to Cart Section -->
                        <div class="space-y-4">
                            <div class="flex items-center gap-4">
                                <div class="flex items-center border border-gray-300 rounded-lg">
                                    <button type="button" onclick="decrementQuantity()" class="px-3 py-2 text-gray-600 hover:text-gray-800">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" id="quantity" value="1" min="1" max="{{ $product->manage_stock ? $product->stock_quantity : 100 }}"
                                           class="w-16 text-center border-0 focus:ring-0" readonly>
                                    <button type="button" onclick="incrementQuantity()" class="px-3 py-2 text-gray-600 hover:text-gray-800">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                <div class="text-sm text-gray-600">
                                    @if($product->manage_stock)
                                        {{ $product->stock_quantity }} in stock
                                    @else
                                        In Stock
                                    @endif
                                </div>
                            </div>

                            <div class="flex gap-4">
                                <button onclick="addToCart({{ $product->id }})"
                                        class="flex-1 bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-lg font-semibold text-lg transition-colors"
                                        id="add-to-cart-btn">
                                    <i class="fas fa-shopping-cart mr-2"></i>
                                    Add to Cart - ${{ number_format($product->getCurrentPrice(), 2) }}
                                </button>

                                <button onclick="toggleWishlist({{ $product->id }})"
                                        class="wishlist-btn bg-gray-100 hover:bg-gray-200 text-gray-700 py-4 px-6 rounded-lg transition-colors {{ $isInWishlist ? 'active' : '' }}"
                                        data-product-id="{{ $product->id }}">
                                    <i class="fas fa-heart mr-2"></i>
                                    <span class="wishlist-text">{{ $isInWishlist ? 'Remove' : 'Wishlist' }}</span>
                                </button>
                            </div>
                        </div>
                    @else
                        <!-- Request Quote Section -->
                        <div class="flex gap-4">
                            <button onclick="openProductQuote('{{ $product->id }}', '{{ $product->name }}', '{{ $product->price }}')"
                                    class="flex-1 bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-lg font-semibold text-lg transition-colors">
                                <i class="fas fa-quote-left mr-2"></i>Request Quote
                            </button>

                            <button onclick="toggleWishlist({{ $product->id }})"
                                    class="wishlist-btn bg-gray-100 hover:bg-gray-200 text-gray-700 py-4 px-6 rounded-lg transition-colors {{ $isInWishlist ? 'active' : '' }}"
                                    data-product-id="{{ $product->id }}">
                                <i class="fas fa-heart mr-2"></i>
                                <span class="wishlist-text">{{ $isInWishlist ? 'Remove' : 'Wishlist' }}</span>
                            </button>
                        </div>
                    @endif

                    <div class="flex justify-center">
                        <button onclick="openQuoteModal()"
                                class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-3 px-6 rounded-lg font-semibold transition-colors">
                            <i class="fas fa-shipping-fast mr-2"></i>Shipping Quote
                        </button>
                    </div>
                    
                    <div class="flex items-center justify-center space-x-6 text-sm text-gray-600">
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt mr-2 text-green-600"></i>
                            Secure Shipping
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-truck mr-2 text-green-600"></i>
                            Fast Delivery
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-headset mr-2 text-green-600"></i>
                            24/7 Support
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Product Description -->
        @if($product->description)
        <div class="mt-16">
            <div class="border-b border-gray-200 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 pb-4">Product Description</h2>
            </div>
            <div class="prose prose-lg max-w-none text-gray-600">
                {!! nl2br(e($product->description)) !!}
            </div>
        </div>
        @endif
        
        <!-- Technical Specifications -->
        @if($product->technical_specifications)
        <div class="mt-16">
            <div class="border-b border-gray-200 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 pb-4">Technical Specifications</h2>
            </div>
            <div class="prose prose-lg max-w-none text-gray-600">
                {!! nl2br(e($product->technical_specifications)) !!}
            </div>
        </div>
        @endif
    </div>
</section>

<!-- Related Products -->
@if($relatedProducts->count() > 0)
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Related Products</h2>
            <p class="text-lg text-gray-600">You might also be interested in these products</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach($relatedProducts as $relatedProduct)
            <div class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 card-hover overflow-hidden">
                <div class="relative">
                    <img src="{{ $relatedProduct->featured_image_url ?? 'https://via.placeholder.com/300x200?text=No+Image' }}" 
                         alt="{{ $relatedProduct->name }}" class="w-full h-48 object-cover">
                    
                    @if($relatedProduct->isOnSale())
                    <span class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                        {{ $relatedProduct->discount_percentage }}% OFF
                    </span>
                    @endif
                    
                    <div class="absolute top-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm">
                        @if($relatedProduct->isOnSale())
                            @currency($relatedProduct->sale_price)
                        @else
                            @currency($relatedProduct->price)
                        @endif
                    </div>
                </div>
                
                <div class="p-4">
                    <h3 class="font-bold text-gray-900 mb-2">{{ $relatedProduct->name }}</h3>
                    <p class="text-gray-600 text-sm mb-4">{{ Str::limit($relatedProduct->short_description, 80) }}</p>
                    
                    <div class="flex gap-2">
                        <a href="{{ route('products.show', $relatedProduct) }}" 
                           class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded text-center text-sm font-medium transition-colors">
                            View
                        </a>
                        <button onclick="openProductQuote('{{ $relatedProduct->id }}', '{{ $relatedProduct->name }}', '{{ $relatedProduct->price }}')" 
                                class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded text-sm font-medium transition-colors">
                            Quote
                        </button>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

@endsection

@push('head')
<!-- Product-specific Open Graph Tags -->
<meta property="og:type" content="product">
<meta property="og:title" content="{{ $product->name }} - {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}">
<meta property="og:description" content="{{ $product->short_description ?? $product->name . ' - Quality product with professional logistics support.' }}">
<meta property="og:url" content="{{ route('products.show', $product) }}">
@if($product->featured_image_url)
<meta property="og:image" content="{{ $product->featured_image_url }}">
<meta property="og:image:width" content="600">
<meta property="og:image:height" content="400">
@endif
<meta property="product:price:amount" content="{{ $product->isOnSale() ? $product->sale_price : $product->price }}">
<meta property="product:price:currency" content="{{ $currentCurrency }}">
<meta property="product:availability" content="{{ $product->isInStock() ? 'in stock' : 'out of stock' }}">
@if($product->category)
<meta property="product:category" content="{{ $product->category->name }}">
@endif

<!-- Twitter Card Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $product->name }} - {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}">
<meta name="twitter:description" content="{{ $product->short_description ?? $product->name . ' - Quality product with professional logistics support.' }}">
@if($product->featured_image_url)
<meta name="twitter:image" content="{{ $product->featured_image_url }}">
@endif

<!-- Canonical URL -->
<link rel="canonical" href="{{ route('products.show', $product) }}">

<!-- Structured Data for Product -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "{{ $product->name }}",
    "description": "{{ $product->short_description ?? $product->name }}",
    "sku": "{{ $product->sku ?? '' }}",
    "url": "{{ route('products.show', $product) }}",
    @if($product->featured_image_url)
    "image": [
        "{{ $product->featured_image_url }}"
        @if(!empty($product->gallery_image_urls))
        @foreach($product->gallery_image_urls as $image)
        ,"{{ $image }}"
        @endforeach
        @endif
    ],
    @endif
    @if($product->category)
    "category": "{{ $product->category->name }}",
    @endif
    @if($product->weight)
    "weight": {
        "@type": "QuantitativeValue",
        "value": "{{ $product->weight }}",
        "unitCode": "KGM"
    },
    @endif
    "offers": {
        "@type": "Offer",
        "price": "{{ $product->isOnSale() ? $product->sale_price : $product->price }}",
        "priceCurrency": "{{ $currentCurrency }}",
        "availability": "{{ $product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock' }}",
        "url": "{{ route('products.show', $product) }}",
        "seller": {
            "@type": "Organization",
            "name": "{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}",
            "url": "{{ route('home') }}"
        }
        @if($product->isOnSale())
        ,"priceValidUntil": "{{ now()->addMonths(3)->format('Y-m-d') }}"
        @endif
    },
    "brand": {
        "@type": "Brand",
        "name": "{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}"
    },
    "manufacturer": {
        "@type": "Organization",
        "name": "{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}"
    },
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "{{ rand(10, 100) }}",
        "bestRating": "5",
        "worstRating": "1"
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "{{ route('home') }}"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Products",
                "item": "{{ route('products.index') }}"
            }
            @if($product->category)
            ,{
                "@type": "ListItem",
                "position": 3,
                "name": "{{ $product->category->name }}",
                "item": "{{ route('categories.show', $product->category) }}"
            }
            ,{
                "@type": "ListItem",
                "position": 4,
                "name": "{{ $product->name }}"
            }
            @else
            ,{
                "@type": "ListItem",
                "position": 3,
                "name": "{{ $product->name }}"
            }
            @endif
        ]
    }
}
</script>
@endpush

<!-- Quote Modal -->
@include('components.quote-modal')

@push('scripts')
<script>
// openProductQuote function is provided by the quote modal component

// Quantity controls
function incrementQuantity() {
    const quantityInput = document.getElementById('quantity');
    const currentValue = parseInt(quantityInput.value);
    const maxValue = parseInt(quantityInput.max);

    if (currentValue < maxValue) {
        quantityInput.value = currentValue + 1;
    }
}

function decrementQuantity() {
    const quantityInput = document.getElementById('quantity');
    const currentValue = parseInt(quantityInput.value);
    const minValue = parseInt(quantityInput.min);

    if (currentValue > minValue) {
        quantityInput.value = currentValue - 1;
    }
}

// Add to cart functionality
function addToCart(productId) {
    const quantity = document.getElementById('quantity').value;
    const button = document.getElementById('add-to-cart-btn');
    const originalText = button.innerHTML;

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Adding to Cart...';

    fetch('/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: parseInt(quantity)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update cart count in header
            updateCartCount();

            // Show success message
            showNotification(data.message, 'success');

            // Reset button
            button.innerHTML = originalText;
            button.disabled = false;
        } else {
            // Show error message
            showNotification(data.message, 'error');

            // Reset button
            button.innerHTML = originalText;
            button.disabled = false;

            // If login required, redirect
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 2000);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');

        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Update cart count in header
function updateCartCount() {
    fetch('/cart/count')
        .then(response => response.json())
        .then(data => {
            const cartCountElement = document.querySelector('.cart-count');
            if (cartCountElement) {
                cartCountElement.textContent = data.count;
            }
        })
        .catch(error => console.error('Error updating cart count:', error));
}

// Wishlist functionality (enhanced)
function toggleWishlist(productId) {
    const button = document.querySelector(`.wishlist-btn[data-product-id="${productId}"]`);
    const icon = button.querySelector('i');
    const text = button.querySelector('.wishlist-text');
    const isActive = button.classList.contains('active');

    // Show loading state
    icon.className = 'fas fa-spinner fa-spin mr-2';
    text.textContent = isActive ? 'Removing...' : 'Adding...';
    button.disabled = true;

    const url = isActive ? '/wishlist/remove' : '/wishlist/add';

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Toggle active state
            button.classList.toggle('active');

            // Update icon and text
            if (button.classList.contains('active')) {
                icon.className = 'fas fa-heart mr-2';
                text.textContent = 'Remove';
                button.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'text-gray-700');
                button.classList.add('bg-red-100', 'hover:bg-red-200', 'text-red-700');
            } else {
                icon.className = 'fas fa-heart mr-2';
                text.textContent = 'Wishlist';
                button.classList.remove('bg-red-100', 'hover:bg-red-200', 'text-red-700');
                button.classList.add('bg-gray-100', 'hover:bg-gray-200', 'text-gray-700');
            }

            showNotification(data.message, 'success');
        } else {
            // Reset to original state
            icon.className = 'fas fa-heart mr-2';
            text.textContent = isActive ? 'Remove' : 'Wishlist';
            showNotification(data.message, 'error');
        }

        button.disabled = false;
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');

        // Reset to original state
        icon.className = 'fas fa-heart mr-2';
        text.textContent = isActive ? 'Remove' : 'Wishlist';
        button.disabled = false;
    });
}
</script>
@endpush
