<?php $__env->startSection('title', 'Product Details'); ?>
<?php $__env->startSection('page-title', $product->name); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Products
        </a>
        <a href="<?php echo e(route('admin.products.edit', $product)); ?>" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Product
        </a>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i> Actions
            </button>
            <ul class="dropdown-menu">
                <li>
                    <form method="POST" action="<?php echo e(route('admin.products.toggle-status', $product)); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="dropdown-item">
                            <?php if($product->is_active): ?>
                                <i class="fas fa-eye-slash me-2"></i> Deactivate
                            <?php else: ?>
                                <i class="fas fa-eye me-2"></i> Activate
                            <?php endif; ?>
                        </button>
                    </form>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <button type="button" class="dropdown-item text-danger" onclick="deleteProduct()">
                        <i class="fas fa-trash me-2"></i> Delete Product
                    </button>
                </li>
            </ul>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Product Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Product Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Product Name</label>
                                <p class="mb-0"><?php echo e($product->name); ?></p>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">SKU</label>
                                    <p class="mb-0">
                                        <code><?php echo e($product->sku); ?></code>
                                        <a href="#" class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('<?php echo e($product->sku); ?>')">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">URL Slug</label>
                                    <p class="mb-0">
                                        <code><?php echo e($product->slug); ?></code>
                                        <a href="#" class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('<?php echo e($product->slug); ?>')">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                    </p>
                                </div>
                            </div>

                            <?php if($product->short_description): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Short Description</label>
                                    <p class="mb-0"><?php echo e($product->short_description); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if($product->description): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Description</label>
                                    <div class="border rounded p-3">
                                        <?php echo nl2br(e($product->description)); ?>

                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Category</label>
                                    <p class="mb-0">
                                        <?php if($product->category): ?>
                                            <a href="<?php echo e(route('admin.categories.show', $product->category)); ?>" class="text-decoration-none">
                                                <i class="fas fa-folder me-2"></i><?php echo e($product->category->name); ?>

                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">No Category</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Created</label>
                                    <p class="mb-0"><?php echo e($product->created_at->format('M d, Y h:i A')); ?></p>
                                    <small class="text-muted"><?php echo e($product->created_at->diffForHumans()); ?></small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <?php if($product->featured_image): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Featured Image</label>
                                    <div>
                                        <img src="<?php echo e(Storage::url($product->featured_image)); ?>" 
                                             alt="<?php echo e($product->name); ?>" 
                                             class="img-thumbnail w-100" 
                                             style="max-height: 300px; object-fit: cover;">
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing & Inventory -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>
                        Pricing & Inventory
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">Regular Price</label>
                            <p class="mb-0 h5"><?php echo \App\Helpers\CurrencyHelper::format($product->price); ?></p>
                        </div>
                        <?php if($product->sale_price): ?>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">Sale Price</label>
                                <p class="mb-0 h5 text-success"><?php echo \App\Helpers\CurrencyHelper::format($product->sale_price); ?></p>
                                <?php if($product->isOnSale()): ?>
                                    <small class="badge bg-danger"><?php echo e($product->getDiscountPercentage()); ?>% OFF</small>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        <?php if($product->cost_price): ?>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">Cost Price</label>
                                <p class="mb-0"><?php echo \App\Helpers\CurrencyHelper::format($product->cost_price); ?></p>
                                <?php if($product->cost_price > 0): ?>
                                    <small class="text-muted">
                                        Profit: <?php echo \App\Helpers\CurrencyHelper::format($product->getCurrentPrice() - $product->cost_price); ?>
                                        (<?php echo e(number_format((($product->getCurrentPrice() - $product->cost_price) / $product->cost_price) * 100, 1)); ?>%)
                                    </small>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Stock Status</label>
                            <div>
                                <?php if($product->manage_stock): ?>
                                    <?php if($product->isInStock()): ?>
                                        <span class="badge bg-success">In Stock (<?php echo e($product->stock_quantity); ?>)</span>
                                    <?php elseif($product->isLowStock()): ?>
                                        <span class="badge bg-warning">Low Stock (<?php echo e($product->stock_quantity); ?>)</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Out of Stock</span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="badge bg-info"><?php echo e(ucwords(str_replace('_', ' ', $product->stock_status))); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if($product->manage_stock && $product->min_stock_level): ?>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Low Stock Alert</label>
                                <p class="mb-0"><?php echo e($product->min_stock_level); ?> units</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Gallery Images -->
            <?php if($product->gallery_images && count($product->gallery_images) > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2"></i>
                            Product Gallery
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php $__currentLoopData = $product->gallery_images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-3 mb-3">
                                    <img src="<?php echo e(Storage::url($image)); ?>" 
                                         alt="<?php echo e($product->name); ?> Gallery" 
                                         class="img-thumbnail w-100" 
                                         style="height: 150px; object-fit: cover; cursor: pointer;"
                                         onclick="showImageModal('<?php echo e(Storage::url($image)); ?>')">
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Status & Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Status & Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Status</label>
                        <div>
                            <?php if($product->is_active): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Inactive</span>
                            <?php endif; ?>
                            
                            <?php if($product->is_featured): ?>
                                <span class="badge bg-warning ms-2">Featured</span>
                            <?php endif; ?>

                            <?php if($product->is_digital): ?>
                                <span class="badge bg-info ms-2">Digital</span>
                            <?php endif; ?>

                            <?php if($product->is_virtual): ?>
                                <span class="badge bg-purple ms-2">Virtual</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if($product->weight || $product->dimensions): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Shipping Info</label>
                            <?php if($product->weight): ?>
                                <p class="mb-1"><small>Weight: <?php echo e($product->weight); ?> lbs</small></p>
                            <?php endif; ?>
                            <?php if($product->dimensions): ?>
                                <p class="mb-0">
                                    <small>
                                        Dimensions: <?php echo e($product->dimensions['length'] ?? 0); ?>" × 
                                        <?php echo e($product->dimensions['width'] ?? 0); ?>" × 
                                        <?php echo e($product->dimensions['height'] ?? 0); ?>"
                                    </small>
                                </p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Last Updated</label>
                        <p class="mb-0 small"><?php echo e($product->updated_at->format('M d, Y h:i A')); ?></p>
                        <small class="text-muted"><?php echo e($product->updated_at->diffForHumans()); ?></small>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.products.edit', $product)); ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            Edit Product
                        </a>
                        <a href="<?php echo e(route('products.show', $product->slug)); ?>" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-eye me-2"></i>
                            Preview on Site
                        </a>
                        <button type="button" class="btn btn-outline-secondary" onclick="duplicateProduct(<?php echo e($product->id); ?>, '<?php echo e($product->name); ?>')">
                            <i class="fas fa-copy me-2"></i>
                            Duplicate Product
                        </button>
                        <a href="<?php echo e(route('admin.products.analytics', $product)); ?>" class="btn btn-outline-warning">
                            <i class="fas fa-chart-line me-2"></i>
                            View Analytics
                        </a>
                    </div>
                </div>
            </div>

            <!-- SEO Information -->
            <?php if($product->meta_title || $product->meta_description || $product->meta_keywords): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            SEO Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if($product->meta_title): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Meta Title</label>
                                <p class="mb-0 small"><?php echo e($product->meta_title); ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if($product->meta_description): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Meta Description</label>
                                <p class="mb-0 small"><?php echo e($product->meta_description); ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if($product->meta_keywords): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Meta Keywords</label>
                                <p class="mb-0 small"><?php echo e($product->meta_keywords); ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if($product->tags): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Tags</label>
                                <div>
                                    <?php $__currentLoopData = $product->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-light text-dark me-1"><?php echo e($tag); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Delete Product
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the product <strong><?php echo e($product->name); ?></strong>?</p>
                    <p class="text-muted">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="<?php echo e(route('admin.products.destroy', $product)); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>
                            Delete Product
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Product Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="#" alt="Product Image" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function deleteProduct() {
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        Copied to clipboard!
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        });
    }

    function showImageModal(imageSrc) {
        document.getElementById('modalImage').src = imageSrc;
        new bootstrap.Modal(document.getElementById('imageModal')).show();
    }

    function duplicateProduct(productId, productName) {
        if (confirm(`Are you sure you want to duplicate "${productName}"?\n\nThis will create a copy of the product that you can then edit. The copy will be inactive by default.`)) {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/ecommerce/products/${productId}/duplicate`;

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            form.appendChild(csrfToken);

            // Add to body and submit
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/admin/products/show.blade.php ENDPATH**/ ?>