<?php $__env->startSection('page-title', 'Quote Details - ' . $quote->quote_number); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Quote Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-1">
                                <i class="fas fa-file-invoice me-2"></i>
                                Quote #<?php echo e($quote->quote_number); ?>

                            </h4>
                            <p class="text-muted mb-0">
                                Created on <?php echo e($quote->created_at->format('M j, Y \a\t g:i A')); ?>

                            </p>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-<?php echo e($quote->status_badge_color); ?> fs-6 mb-2">
                                <?php echo e($quote->formatted_status); ?>

                            </span>
                            <br>
                            <span class="badge bg-<?php echo e($quote->priority_badge_color); ?>">
                                <?php echo e($quote->formatted_priority); ?> Priority
                            </span>
                        </div>
                    </div>
                </div>
                
                <?php if($quote->isQuoted() && !$quote->isExpired()): ?>
                <div class="card-body border-top bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="text-success mb-1">
                                <i class="fas fa-check-circle me-2"></i>
                                Your quote is ready!
                            </h5>
                            <p class="mb-0">
                                <?php if($quote->expires_at): ?>
                                    This quote expires on <?php echo e($quote->expires_at->format('M j, Y')); ?>

                                    <?php if($quote->getDaysUntilExpiry() <= 3): ?>
                                        <span class="text-danger fw-bold">(<?php echo e($quote->getDaysUntilExpiry()); ?> days remaining)</span>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <form method="POST" action="<?php echo e(route('customer.quotes.accept', $quote)); ?>" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-success me-2">
                                    <i class="fas fa-check me-1"></i> Accept Quote
                                </button>
                            </form>
                            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                <i class="fas fa-times me-1"></i> Reject
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quote Details -->
        <div class="col-lg-8">
            <!-- Service Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        Service Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Quote Type:</td>
                                    <td><?php echo e(ucwords($quote->quote_type)); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Service Type:</td>
                                    <td><?php echo e($quote->formatted_service_type); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Delivery Speed:</td>
                                    <td><?php echo e(ucwords($quote->delivery_speed)); ?></td>
                                </tr>
                                <?php if($quote->preferred_pickup_date): ?>
                                <tr>
                                    <td class="fw-bold">Preferred Pickup:</td>
                                    <td><?php echo e($quote->preferred_pickup_date->format('M j, Y')); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <?php if($quote->required_delivery_date): ?>
                                <tr>
                                    <td class="fw-bold">Required Delivery:</td>
                                    <td><?php echo e($quote->required_delivery_date->format('M j, Y')); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td class="fw-bold">Insurance:</td>
                                    <td>
                                        <?php if($quote->insurance_required): ?>
                                            <span class="badge bg-success">Required</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Not Required</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Signature:</td>
                                    <td>
                                        <?php if($quote->signature_required): ?>
                                            <span class="badge bg-success">Required</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Not Required</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if($quote->description): ?>
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted"><?php echo e($quote->description); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Shipping Details -->
            <?php if($quote->isShippingQuote()): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Shipping Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fas fa-arrow-up me-1"></i>
                                Origin
                            </h6>
                            <address class="mb-0">
                                <?php echo e($quote->origin_address); ?><br>
                                <?php echo e($quote->origin_city); ?>, <?php echo e($quote->origin_state); ?> <?php echo e($quote->origin_postal_code); ?><br>
                                <?php echo e($quote->origin_country); ?>

                            </address>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">
                                <i class="fas fa-arrow-down me-1"></i>
                                Destination
                            </h6>
                            <address class="mb-0">
                                <?php echo e($quote->destination_address); ?><br>
                                <?php echo e($quote->destination_city); ?>, <?php echo e($quote->destination_state); ?> <?php echo e($quote->destination_postal_code); ?><br>
                                <?php echo e($quote->destination_country); ?>

                            </address>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Package Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        Package Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Package Count:</td>
                                    <td><?php echo e($quote->package_count); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Total Weight:</td>
                                    <td><?php echo e($quote->total_weight); ?> <?php echo e($quote->weight_unit); ?></td>
                                </tr>
                                <?php if($quote->dimensions): ?>
                                <tr>
                                    <td class="fw-bold">Dimensions:</td>
                                    <td>
                                        <?php echo e($quote->dimensions['length'] ?? 0); ?> × 
                                        <?php echo e($quote->dimensions['width'] ?? 0); ?> × 
                                        <?php echo e($quote->dimensions['height'] ?? 0); ?> 
                                        <?php echo e($quote->dimensions['unit'] ?? 'cm'); ?>

                                    </td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Package Type:</td>
                                    <td><?php echo e($quote->package_type); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Declared Value:</td>
                                    <td><?php echo \App\Helpers\CurrencyHelper::format($quote->declared_value); ?> <?php echo e($quote->currency); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Special Handling:</td>
                                    <td>
                                        <?php if($quote->fragile): ?>
                                            <span class="badge bg-warning me-1">Fragile</span>
                                        <?php endif; ?>
                                        <?php if($quote->hazardous): ?>
                                            <span class="badge bg-danger">Hazardous</span>
                                        <?php endif; ?>
                                        <?php if(!$quote->fragile && !$quote->hazardous): ?>
                                            <span class="text-muted">None</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if($quote->package_description): ?>
                    <div class="mt-3">
                        <h6>Package Description:</h6>
                        <p class="text-muted"><?php echo e($quote->package_description); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Product Information (for product quotes) -->
            <?php if($quote->isProductQuote() && $quote->products): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        Products (<?php echo e($quote->getProductsCount()); ?> items)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $quote->getProductsWithDetails(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <strong><?php echo e($item['product']->name); ?></strong>
                                        <?php if($item['notes']): ?>
                                            <br><small class="text-muted"><?php echo e($item['notes']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($item['quantity']); ?></td>
                                    <td><?php echo \App\Helpers\CurrencyHelper::format($item['price_at_time']); ?></td>
                                    <td><?php echo \App\Helpers\CurrencyHelper::format($item['total']); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr class="table-active">
                                    <th colspan="3">Products Total:</th>
                                    <th><?php echo \App\Helpers\CurrencyHelper::format($quote->products_total); ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Pricing Information -->
            <?php if($quote->isQuoted()): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>
                        Pricing
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($quote->pricing_breakdown): ?>
                        <?php $__currentLoopData = $quote->pricing_breakdown; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item => $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex justify-content-between">
                            <span><?php echo e(ucwords(str_replace('_', ' ', $item))); ?>:</span>
                            <span><?php echo \App\Helpers\CurrencyHelper::format($amount); ?></span>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <hr>
                    <?php endif; ?>
                    
                    <?php if($quote->discount_amount > 0): ?>
                    <div class="d-flex justify-content-between text-success">
                        <span>Discount:</span>
                        <span>-<?php echo \App\Helpers\CurrencyHelper::format($quote->discount_amount); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="d-flex justify-content-between fw-bold fs-5 border-top pt-2">
                        <span>Total:</span>
                        <span class="text-primary"><?php echo \App\Helpers\CurrencyHelper::format($quote->final_price ?? $quote->quoted_price); ?> <?php echo e($quote->currency); ?></span>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Contact Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        Contact Information
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td class="fw-bold">Name:</td>
                            <td><?php echo e($quote->customer_name); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Email:</td>
                            <td><?php echo e($quote->customer_email); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Phone:</td>
                            <td><?php echo e($quote->customer_phone); ?></td>
                        </tr>
                        <?php if($quote->company_name): ?>
                        <tr>
                            <td class="fw-bold">Company:</td>
                            <td><?php echo e($quote->company_name); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('customer.quotes.index')); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Quotes
                        </a>
                        
                        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#notesModal">
                            <i class="fas fa-sticky-note me-1"></i> Add Notes
                        </button>
                        
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> Print Quote
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Quote Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Quote</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reject this quote? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="<?php echo e(route('customer.quotes.reject', $quote)); ?>" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-1"></i> Reject Quote
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add Notes Modal -->
<div class="modal fade" id="notesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="<?php echo e(route('customer.quotes.add-notes', $quote)); ?>">
                <?php echo csrf_field(); ?>
                <div class="modal-header">
                    <h5 class="modal-title">Add Notes</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="customer_notes" class="form-label">Your Notes</label>
                        <textarea class="form-control" id="customer_notes" name="customer_notes" rows="4" 
                                  placeholder="Add any additional notes or requirements..."><?php echo e($quote->customer_notes); ?></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Save Notes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/customer/quotes/show.blade.php ENDPATH**/ ?>