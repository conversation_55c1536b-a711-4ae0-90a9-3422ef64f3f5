<?php $__env->startSection('title', 'Newsletter Unsubscribed - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics')); ?>
<?php $__env->startSection('description', 'You have been successfully unsubscribed from our newsletter.'); ?>

<?php $__env->startSection('content'); ?>

<!-- Hero Section -->
<?php if (isset($component)) { $__componentOriginala9d931d4f11b4d2850df99e991db1dca = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala9d931d4f11b4d2850df99e991db1dca = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-hero','data' => ['title' => 'Newsletter Unsubscribed','subtitle' => 'You have been successfully unsubscribed from our newsletter','description' => 'We\'re sorry to see you go! You can always resubscribe by visiting our website.','breadcrumbs' => [
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Newsletter Unsubscribed']
    ],'gradient' => 'from-blue-900 via-green-800 to-gray-900']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Newsletter Unsubscribed','subtitle' => 'You have been successfully unsubscribed from our newsletter','description' => 'We\'re sorry to see you go! You can always resubscribe by visiting our website.','breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Newsletter Unsubscribed']
    ]),'gradient' => 'from-blue-900 via-green-800 to-gray-900']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $attributes = $__attributesOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $component = $__componentOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__componentOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>

<!-- Main Content -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-2xl shadow-lg p-8 text-center">
                <div class="mb-6">
                    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check-circle text-green-600 text-3xl"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">Successfully Unsubscribed</h1>
                    <p class="text-lg text-gray-600">
                        You have been successfully unsubscribed from our newsletter.
                    </p>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="text-sm text-blue-800">
                        <strong>Email:</strong> <?php echo e($subscriber->email); ?>

                        <br>
                        <strong>Unsubscribed:</strong> <?php echo e(now()->format('F j, Y \a\t g:i A')); ?>

                    </div>
                </div>

                <p class="text-gray-600 mb-8">
                    We're sorry to see you go! If you change your mind, you can always resubscribe
                    by visiting our website and signing up again.
                </p>

                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?php echo e(route('home')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        Return to Homepage
                    </a>

                    <a href="<?php echo e(route('contact')); ?>" class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-8 py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-envelope mr-2"></i>
                        Contact Us
                    </a>
                </div>

                <hr class="my-8 border-gray-200">

                <div class="text-gray-500 text-sm">
                    If you believe this was done in error, please
                    <a href="<?php echo e(route('contact')); ?>" class="text-green-600 hover:text-green-700 font-medium">contact us</a> for assistance.
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/frontend/newsletter/unsubscribed.blade.php ENDPATH**/ ?>