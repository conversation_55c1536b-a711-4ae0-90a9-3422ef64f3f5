{"version": 1, "defects": {"Tests\\Feature\\CustomerQuoteTest::customer_can_view_quote_creation_form": 7, "Tests\\Feature\\CustomerQuoteTest::customer_can_submit_shipping_quote_request": 7, "Tests\\Feature\\CustomerQuoteTest::customer_can_submit_product_quote_request": 7, "Tests\\Feature\\CustomerQuoteTest::shipping_quote_validation_fails_with_missing_required_fields": 7, "Tests\\Feature\\CustomerQuoteTest::product_quote_validation_fails_with_missing_products": 7, "Tests\\Feature\\CustomerQuoteTest::ajax_quote_submission_returns_json_response": 7, "Tests\\Feature\\CustomerQuoteTest::ajax_quote_submission_returns_validation_errors": 7, "Tests\\Feature\\CustomerQuoteTest::customer_can_lookup_their_quotes": 7, "Tests\\Feature\\CustomerQuoteTest::customer_cannot_lookup_other_customers_quotes": 7, "Tests\\Feature\\CustomerQuoteTest::quote_lookup_fails_with_invalid_data": 7, "Tests\\Feature\\CustomerQuoteTest::customer_can_view_their_quote_details": 7, "Tests\\Feature\\CustomerQuoteTest::customer_cannot_view_other_customers_quotes": 7, "Tests\\Feature\\LiveChatSystemTest::visitor_can_start_chat_session": 8, "Tests\\Feature\\LiveChatSystemTest::visitor_can_send_message": 8, "Tests\\Feature\\LiveChatSystemTest::visitor_can_get_messages": 8, "Tests\\Feature\\LiveChatSystemTest::admin_can_view_live_chat_dashboard": 8, "Tests\\Feature\\LiveChatSystemTest::admin_can_assign_chat_session": 8, "Tests\\Feature\\LiveChatSystemTest::admin_can_send_message_to_visitor": 8, "Tests\\Feature\\LiveChatSystemTest::admin_can_close_chat_session": 8, "Tests\\Feature\\LiveChatSystemTest::admin_can_get_live_chat_stats": 8, "Tests\\Feature\\LiveChatSystemTest::visitor_cannot_access_admin_routes": 8, "Tests\\Feature\\CartTest::user_can_add_product_to_cart": 7, "Tests\\Feature\\CartTest::user_cannot_add_product_without_authentication": 8, "Tests\\Feature\\CartTest::user_cannot_add_more_than_available_stock": 8, "Tests\\Feature\\CartTest::user_can_update_cart_item_quantity": 8, "Tests\\Feature\\CartTest::user_can_remove_item_by_setting_quantity_to_zero": 8, "Tests\\Feature\\CartTest::user_can_remove_product_from_cart": 8, "Tests\\Feature\\CartTest::user_can_clear_entire_cart": 8, "Tests\\Feature\\CartTest::user_can_get_cart_count": 8, "Tests\\Feature\\CartTest::user_can_save_item_for_later": 8, "Tests\\Feature\\CartTest::cart_totals_are_calculated_correctly": 7, "Tests\\Feature\\CartTest::cart_view_displays_correctly": 8, "Tests\\Feature\\CheckoutTest::user_can_view_checkout_page_with_items_in_cart": 8, "Tests\\Feature\\CheckoutTest::user_cannot_access_checkout_with_empty_cart": 8, "Tests\\Feature\\CheckoutTest::user_cannot_access_checkout_without_authentication": 7, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_manual_payment": 8, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_paypal_payment": 8, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_stripe_payment": 8, "Tests\\Feature\\CheckoutTest::checkout_fails_with_invalid_address": 8, "Tests\\Feature\\CheckoutTest::checkout_fails_with_invalid_payment_method": 8, "Tests\\Feature\\CheckoutTest::checkout_fails_when_user_not_authenticated": 7, "Tests\\Feature\\CheckoutTest::checkout_fails_with_empty_cart": 8, "Tests\\Feature\\CheckoutTest::user_can_view_order_confirmation": 7, "Tests\\Feature\\CheckoutTest::user_cannot_view_other_users_order_confirmation": 8, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_manual_payment": 7, "Tests\\Feature\\CommunicationsSystemTest::admin_can_view_contacts": 8, "Tests\\Feature\\CommunicationsSystemTest::admin_can_view_newsletter_subscribers": 8, "Tests\\Feature\\CommunicationsSystemTest::admin_can_update_contact_status": 8, "Tests\\Feature\\CommunicationsSystemTest::newsletter_unsubscribe_works": 8, "Tests\\Feature\\CommunicationsSystemTest::admin_can_bulk_delete_contacts": 8, "Tests\\Feature\\CustomerQuoteTest::checkbox_fields_are_properly_validated_and_processed": 7, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_paypal_payment": 8, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_stripe_payment": 8, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 8, "Tests\\Unit\\ImageProcessingServiceTest::it_can_validate_valid_image": 8, "Tests\\Unit\\ImageProcessingServiceTest::it_rejects_oversized_images": 8, "Tests\\Unit\\ImageProcessingServiceTest::it_generates_unique_filenames": 8, "Tests\\Unit\\OrderPlacementTest::it_can_create_order_with_manual_payment": 7, "Tests\\Unit\\OrderPlacementTest::it_creates_order_items_correctly": 7, "Tests\\Unit\\OrderPlacementTest::it_reduces_product_stock_after_order_creation": 8, "Tests\\Unit\\OrderPlacementTest::it_clears_cart_after_successful_order": 8, "Tests\\Unit\\OrderPlacementTest::it_generates_unique_order_number": 8, "Tests\\Unit\\OrderPlacementTest::it_calculates_order_totals_correctly": 7, "Tests\\Unit\\OrderPlacementTest::it_stores_shipping_and_billing_addresses": 8, "Tests\\Unit\\OrderPlacementTest::it_handles_same_shipping_and_billing_address": 7, "Tests\\Unit\\OrderPlacementTest::it_fails_when_cart_is_empty": 8, "Tests\\Unit\\OrderPlacementTest::it_fails_when_insufficient_stock": 7, "Tests\\Unit\\OrderPlacementTest::it_supports_different_payment_methods": 8, "Tests\\Feature\\CheckoutIntegrationTest::user_cannot_checkout_with_empty_cart": 7, "Tests\\Feature\\CheckoutIntegrationTest::user_can_place_order_with_manual_payment": 7, "Tests\\Feature\\CheckoutIntegrationTest::user_can_place_order_with_paypal_payment": 7, "Tests\\Feature\\CheckoutIntegrationTest::user_can_place_order_with_stripe_payment": 7, "Tests\\Feature\\CheckoutIntegrationTest::checkout_fails_when_user_not_authenticated": 7, "Tests\\Feature\\CheckoutIntegrationTest::checkout_fails_with_empty_cart": 7, "Tests\\Feature\\CheckoutIntegrationTest::user_cannot_view_other_users_order_confirmation": 7}, "times": {"Tests\\Unit\\ImageProcessingServiceTest::it_can_validate_valid_image": 0.021, "Tests\\Unit\\ImageProcessingServiceTest::it_rejects_oversized_images": 0.003, "Tests\\Unit\\ImageProcessingServiceTest::it_generates_unique_filenames": 0.007, "Tests\\Feature\\CartTest::user_can_add_product_to_cart": 0.013, "Tests\\Feature\\CartTest::user_cannot_add_product_without_authentication": 0.006, "Tests\\Feature\\CartTest::user_cannot_add_more_than_available_stock": 0.009, "Tests\\Feature\\CartTest::user_can_update_cart_item_quantity": 0.014, "Tests\\Feature\\CartTest::user_can_remove_item_by_setting_quantity_to_zero": 0.014, "Tests\\Feature\\CartTest::user_can_remove_product_from_cart": 0.014, "Tests\\Feature\\CartTest::user_can_clear_entire_cart": 0.017, "Tests\\Feature\\CartTest::user_can_get_cart_count": 0.009, "Tests\\Feature\\CartTest::user_can_save_item_for_later": 0.013, "Tests\\Feature\\CartTest::cart_totals_are_calculated_correctly": 0.011, "Tests\\Feature\\CartTest::cart_view_displays_correctly": 0.028, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_manual_payment": 0.017, "Tests\\Feature\\CheckoutTest::user_can_view_checkout_page_with_items_in_cart": 0.024, "Tests\\Feature\\CheckoutTest::user_cannot_access_checkout_with_empty_cart": 0.006, "Tests\\Feature\\CheckoutTest::user_cannot_access_checkout_without_authentication": 0.006, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_manual_payment": 0.12, "Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.008, "Tests\\Feature\\Auth\\AuthenticationTest::test_login_screen_can_be_rendered": 0.08, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_authenticate_using_the_login_screen": 0.044, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_not_authenticate_with_invalid_password": 0.224, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_logout": 0.008, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_verification_screen_can_be_rendered": 0.014, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_can_be_verified": 0.015, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_is_not_verified_with_invalid_hash": 0.014, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_confirm_password_screen_can_be_rendered": 0.014, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_can_be_confirmed": 0.011, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_is_not_confirmed_with_invalid_password": 0.222, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_screen_can_be_rendered": 0.014, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_can_be_requested": 0.228, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_screen_can_be_rendered": 0.223, "Tests\\Feature\\Auth\\PasswordResetTest::test_password_can_be_reset_with_valid_token": 0.23, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_password_can_be_updated": 0.012, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_correct_password_must_be_provided_to_update_password": 0.009, "Tests\\Feature\\Auth\\RegistrationTest::test_registration_screen_can_be_rendered": 0.019, "Tests\\Feature\\Auth\\RegistrationTest::test_new_users_can_register": 0.01, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_paypal_payment": 0.02, "Tests\\Feature\\CheckoutTest::user_can_place_order_with_stripe_payment": 0.018, "Tests\\Feature\\CheckoutTest::checkout_fails_with_invalid_address": 0.016, "Tests\\Feature\\CheckoutTest::checkout_fails_with_invalid_payment_method": 0.011, "Tests\\Feature\\CheckoutTest::checkout_fails_when_user_not_authenticated": 0.007, "Tests\\Feature\\CheckoutTest::checkout_fails_with_empty_cart": 0.006, "Tests\\Feature\\CheckoutTest::user_can_view_order_confirmation": 0.033, "Tests\\Feature\\CheckoutTest::user_cannot_view_other_users_order_confirmation": 0.012, "Tests\\Feature\\CommunicationsSystemTest::contact_form_submission_works": 0.023, "Tests\\Feature\\CommunicationsSystemTest::newsletter_subscription_works": 0.013, "Tests\\Feature\\CommunicationsSystemTest::admin_can_view_contacts": 0.022, "Tests\\Feature\\CommunicationsSystemTest::admin_can_view_newsletter_subscribers": 0.014, "Tests\\Feature\\CommunicationsSystemTest::admin_can_update_contact_status": 0.008, "Tests\\Feature\\CommunicationsSystemTest::newsletter_unsubscribe_works": 0.025, "Tests\\Feature\\CommunicationsSystemTest::contact_with_newsletter_subscription_works": 0.01, "Tests\\Feature\\CommunicationsSystemTest::admin_can_bulk_delete_contacts": 0.008, "Tests\\Feature\\CommunicationsSystemTest::admin_can_import_newsletter_subscribers": 0.007, "Tests\\Feature\\CustomerQuoteTest::customer_can_view_quote_creation_form": 0.014, "Tests\\Feature\\CustomerQuoteTest::customer_can_submit_shipping_quote_request": 0.01, "Tests\\Feature\\CustomerQuoteTest::customer_can_submit_product_quote_request": 0.013, "Tests\\Feature\\CustomerQuoteTest::shipping_quote_validation_fails_with_missing_required_fields": 0.008, "Tests\\Feature\\CustomerQuoteTest::product_quote_validation_fails_with_missing_products": 0.006, "Tests\\Feature\\CustomerQuoteTest::ajax_quote_submission_returns_json_response": 0.008, "Tests\\Feature\\CustomerQuoteTest::ajax_quote_submission_returns_validation_errors": 0.007, "Tests\\Feature\\CustomerQuoteTest::customer_can_lookup_their_quotes": 0.013, "Tests\\Feature\\CustomerQuoteTest::customer_cannot_lookup_other_customers_quotes": 0.008, "Tests\\Feature\\CustomerQuoteTest::quote_lookup_fails_with_invalid_data": 0.009, "Tests\\Feature\\CustomerQuoteTest::customer_can_view_their_quote_details": 0.017, "Tests\\Feature\\CustomerQuoteTest::customer_cannot_view_other_customers_quotes": 0.012, "Tests\\Feature\\CustomerQuoteTest::checkbox_fields_are_properly_validated_and_processed": 0.013, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_paypal_payment": 0.018, "Tests\\Feature\\EcommerceFlowTest::complete_ecommerce_flow_with_stripe_payment": 0.022, "Tests\\Feature\\EcommerceFlowTest::user_can_edit_cart_multiple_times_before_checkout": 0.046, "Tests\\Feature\\EcommerceFlowTest::stock_management_works_correctly_during_checkout": 0.019, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.026, "Tests\\Feature\\LiveChatSystemTest::visitor_can_start_chat_session": 0.005, "Tests\\Feature\\LiveChatSystemTest::visitor_can_send_message": 0.006, "Tests\\Feature\\LiveChatSystemTest::visitor_can_get_messages": 0.007, "Tests\\Feature\\LiveChatSystemTest::admin_can_view_live_chat_dashboard": 0.013, "Tests\\Feature\\LiveChatSystemTest::admin_can_assign_chat_session": 0.008, "Tests\\Feature\\LiveChatSystemTest::admin_can_send_message_to_visitor": 0.009, "Tests\\Feature\\LiveChatSystemTest::admin_can_close_chat_session": 0.009, "Tests\\Feature\\LiveChatSystemTest::admin_can_get_live_chat_stats": 0.052, "Tests\\Feature\\LiveChatSystemTest::visitor_cannot_access_admin_routes": 0.007, "Tests\\Feature\\ProfileTest::test_profile_page_is_displayed": 0.041, "Tests\\Feature\\ProfileTest::test_profile_information_can_be_updated": 0.011, "Tests\\Feature\\ProfileTest::test_email_verification_status_is_unchanged_when_the_email_address_is_unchanged": 0.007, "Tests\\Feature\\ProfileTest::test_user_can_delete_their_account": 0.008, "Tests\\Feature\\ProfileTest::test_correct_password_must_be_provided_to_delete_account": 0.009, "Tests\\Unit\\OrderPlacementTest::it_can_create_order_with_manual_payment": 0.045, "Tests\\Unit\\OrderPlacementTest::it_creates_order_items_correctly": 0.01, "Tests\\Unit\\OrderPlacementTest::it_reduces_product_stock_after_order_creation": 0.01, "Tests\\Unit\\OrderPlacementTest::it_clears_cart_after_successful_order": 0.015, "Tests\\Unit\\OrderPlacementTest::it_generates_unique_order_number": 0.027, "Tests\\Unit\\OrderPlacementTest::it_calculates_order_totals_correctly": 0.015, "Tests\\Unit\\OrderPlacementTest::it_stores_shipping_and_billing_addresses": 0.008, "Tests\\Unit\\OrderPlacementTest::it_handles_same_shipping_and_billing_address": 0.009, "Tests\\Unit\\OrderPlacementTest::it_fails_when_cart_is_empty": 0.003, "Tests\\Unit\\OrderPlacementTest::it_fails_when_insufficient_stock": 0.02, "Tests\\Unit\\OrderPlacementTest::it_supports_different_payment_methods": 0.022, "Tests\\Unit\\OrderPlacementTest::it_validates_stock_during_order_creation": 0.011, "Tests\\Feature\\CheckoutIntegrationTest::authenticated_user_can_access_checkout_page": 0.492, "Tests\\Feature\\CheckoutIntegrationTest::unauthenticated_user_cannot_access_checkout": 0.009, "Tests\\Feature\\CheckoutIntegrationTest::user_cannot_checkout_with_empty_cart": 0.014, "Tests\\Feature\\CheckoutIntegrationTest::user_can_place_order_with_manual_payment": 0.088, "Tests\\Feature\\CheckoutIntegrationTest::user_can_place_order_with_paypal_payment": 0.051, "Tests\\Feature\\CheckoutIntegrationTest::user_can_place_order_with_stripe_payment": 0.034, "Tests\\Feature\\CheckoutIntegrationTest::checkout_fails_with_invalid_shipping_address": 0.02, "Tests\\Feature\\CheckoutIntegrationTest::checkout_fails_with_invalid_billing_address": 0.014, "Tests\\Feature\\CheckoutIntegrationTest::checkout_fails_with_invalid_payment_method": 0.017, "Tests\\Feature\\CheckoutIntegrationTest::checkout_fails_when_user_not_authenticated": 0.005, "Tests\\Feature\\CheckoutIntegrationTest::checkout_fails_with_empty_cart": 0.014, "Tests\\Feature\\CheckoutIntegrationTest::checkout_reduces_product_stock": 0.018, "Tests\\Feature\\CheckoutIntegrationTest::checkout_clears_cart_after_successful_order": 0.024, "Tests\\Feature\\CheckoutIntegrationTest::user_can_view_order_confirmation": 0.033, "Tests\\Feature\\CheckoutIntegrationTest::user_cannot_view_other_users_order_confirmation": 0.025}}