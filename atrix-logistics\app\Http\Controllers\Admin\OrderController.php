<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;

class OrderController extends Controller
{
    /**
     * Display a listing of orders
     */
    public function index(Request $request): View
    {
        $query = Order::with(['customer', 'items'])
                     ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->paginate(20)->withQueryString();

        // Get statistics
        $stats = [
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'processing_orders' => Order::whereIn('status', ['confirmed', 'processing'])->count(),
            'shipped_orders' => Order::where('status', 'shipped')->count(),
            'delivered_orders' => Order::where('status', 'delivered')->count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'pending_payments' => Order::where('payment_status', 'pending')->sum('total_amount'),
        ];

        return view('admin.orders.index', compact('orders', 'stats'));
    }

    /**
     * Show the form for creating a new order
     */
    public function create(): View
    {
        $customers = User::where('role', 'customer')->orderBy('name')->get();
        $products = Product::where('is_active', true)->orderBy('name')->get();

        return view('admin.orders.create', compact('customers', 'products'));
    }

    /**
     * Store a newly created order
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'customer_id' => 'nullable|exists:users,id',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',

            // Billing address
            'billing_first_name' => 'required|string|max:255',
            'billing_last_name' => 'required|string|max:255',
            'billing_company' => 'nullable|string|max:255',
            'billing_address_1' => 'required|string|max:255',
            'billing_address_2' => 'nullable|string|max:255',
            'billing_city' => 'required|string|max:255',
            'billing_state' => 'required|string|max:255',
            'billing_postal_code' => 'required|string|max:20',
            'billing_country' => 'required|string|max:255',

            // Shipping address
            'shipping_first_name' => 'required|string|max:255',
            'shipping_last_name' => 'required|string|max:255',
            'shipping_company' => 'nullable|string|max:255',
            'shipping_address_1' => 'required|string|max:255',
            'shipping_address_2' => 'nullable|string|max:255',
            'shipping_city' => 'required|string|max:255',
            'shipping_state' => 'required|string|max:255',
            'shipping_postal_code' => 'required|string|max:20',
            'shipping_country' => 'required|string|max:255',

            // Order details
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',

            'shipping_amount' => 'nullable|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'admin_notes' => 'nullable|string',
        ]);

        // Calculate totals
        $subtotal = 0;
        foreach ($validated['items'] as $item) {
            $subtotal += $item['unit_price'] * $item['quantity'];
        }

        $shippingAmount = $validated['shipping_amount'] ?? 0;
        $taxAmount = $validated['tax_amount'] ?? 0;
        $discountAmount = $validated['discount_amount'] ?? 0;
        $totalAmount = $subtotal + $shippingAmount + $taxAmount - $discountAmount;

        // Create order
        $order = Order::create(array_merge($validated, [
            'subtotal' => $subtotal,
            'shipping_amount' => $shippingAmount,
            'tax_amount' => $taxAmount,
            'discount_amount' => $discountAmount,
            'total_amount' => $totalAmount,
            'status' => 'pending',
            'payment_status' => 'pending',
        ]));

        // Create order items
        foreach ($validated['items'] as $itemData) {
            $product = Product::find($itemData['product_id']);

            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'product_sku' => $product->sku,
                'product_description' => $product->short_description,
                'unit_price' => $itemData['unit_price'],
                'quantity' => $itemData['quantity'],
                'total_price' => $itemData['unit_price'] * $itemData['quantity'],
            ]);

            // Update product stock if managed
            if ($product->manage_stock) {
                $product->decrement('stock_quantity', $itemData['quantity']);
            }
        }

        return redirect()->route('admin.orders.show', $order)
                        ->with('success', 'Order created successfully.');
    }

    /**
     * Display the specified order
     */
    public function show(Order $order): View
    {
        $order->load(['customer', 'items.product']);

        return view('admin.orders.show', compact('order'));
    }

    /**
     * Show the form for editing the specified order
     */
    public function edit(Order $order): View
    {
        $customers = User::where('role', 'customer')->orderBy('name')->get();
        $products = Product::where('is_active', true)->orderBy('name')->get();

        $order->load(['customer', 'items.product']);

        return view('admin.orders.edit', compact('order', 'customers', 'products'));
    }

    /**
     * Update the specified order
     */
    public function update(Request $request, Order $order): RedirectResponse
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,confirmed,processing,shipped,delivered,cancelled,refunded',
            'payment_status' => 'required|in:pending,paid,partially_paid,refunded,failed',
            'shipping_method' => 'nullable|string|max:255',
            'tracking_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'admin_notes' => 'nullable|string',
        ]);

        // Handle status changes
        if ($validated['status'] !== $order->status) {
            if ($validated['status'] === 'shipped' && !$order->shipped_at) {
                $validated['shipped_at'] = now();
            }

            if ($validated['status'] === 'delivered' && !$order->delivered_at) {
                $validated['delivered_at'] = now();
            }
        }

        // Handle payment status changes
        if ($validated['payment_status'] === 'paid' && $order->payment_status !== 'paid') {
            $validated['paid_at'] = now();
        }

        $order->update($validated);

        return redirect()->route('admin.orders.show', $order)
                        ->with('success', 'Order updated successfully.');
    }

    /**
     * Remove the specified order
     */
    public function destroy(Order $order): RedirectResponse
    {
        // Restore stock for cancelled orders
        if (!$order->isCancelled()) {
            foreach ($order->items as $item) {
                if ($item->product && $item->product->manage_stock) {
                    $item->product->increment('stock_quantity', $item->quantity);
                }
            }
        }

        $order->delete();

        return redirect()->route('admin.orders.index')
                        ->with('success', 'Order deleted successfully.');
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, Order $order): JsonResponse
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,confirmed,processing,shipped,delivered,cancelled,refunded',
        ]);

        $oldStatus = $order->status;
        $order->update($validated);

        // Handle status-specific actions
        if ($validated['status'] === 'shipped' && $oldStatus !== 'shipped') {
            $order->update(['shipped_at' => now()]);
        }

        if ($validated['status'] === 'delivered' && $oldStatus !== 'delivered') {
            $order->update(['delivered_at' => now()]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully.',
            'status' => $order->formatted_status,
            'badge_color' => $order->status_badge_color,
        ]);
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus(Request $request, Order $order): JsonResponse
    {
        $validated = $request->validate([
            'payment_status' => 'required|in:pending,paid,partially_paid,refunded,failed',
        ]);

        if ($validated['payment_status'] === 'paid' && $order->payment_status !== 'paid') {
            $validated['paid_at'] = now();
        }

        $order->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Payment status updated successfully.',
            'payment_status' => $order->formatted_payment_status,
            'badge_color' => $order->payment_status_badge_color,
        ]);
    }

    /**
     * Bulk update orders
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:orders,id',
            'action' => 'required|in:confirm,process,ship,deliver,cancel',
        ]);

        $orders = Order::whereIn('id', $validated['order_ids'])->get();
        $updated = 0;

        foreach ($orders as $order) {
            $newStatus = match($validated['action']) {
                'confirm' => 'confirmed',
                'process' => 'processing',
                'ship' => 'shipped',
                'deliver' => 'delivered',
                'cancel' => 'cancelled',
            };

            if ($order->status !== $newStatus) {
                $order->update(['status' => $newStatus]);

                if ($newStatus === 'shipped') {
                    $order->update(['shipped_at' => now()]);
                } elseif ($newStatus === 'delivered') {
                    $order->update(['delivered_at' => now()]);
                }

                $updated++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully updated {$updated} orders.",
        ]);
    }
}
