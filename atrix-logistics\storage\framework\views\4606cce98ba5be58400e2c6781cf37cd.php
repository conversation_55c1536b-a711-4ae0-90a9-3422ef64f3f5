<?php $__env->startSection('title', 'Category Details'); ?>
<?php $__env->startSection('page-title', $category->name); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.categories.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Categories
        </a>
        <a href="<?php echo e(route('admin.categories.edit', $category)); ?>" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Category
        </a>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i> Actions
            </button>
            <ul class="dropdown-menu">
                <li>
                    <form method="POST" action="<?php echo e(route('admin.categories.toggle-status', $category)); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="dropdown-item">
                            <?php if($category->is_active): ?>
                                <i class="fas fa-eye-slash me-2"></i> Deactivate
                            <?php else: ?>
                                <i class="fas fa-eye me-2"></i> Activate
                            <?php endif; ?>
                        </button>
                    </form>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <button type="button" class="dropdown-item text-danger" onclick="deleteCategory()">
                        <i class="fas fa-trash me-2"></i> Delete Category
                    </button>
                </li>
            </ul>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Category Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Category Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Category Name</label>
                                <p class="mb-0"><?php echo e($category->name); ?></p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">URL Slug</label>
                                <p class="mb-0">
                                    <code><?php echo e($category->slug); ?></code>
                                    <a href="#" class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('<?php echo e($category->slug); ?>')">
                                        <i class="fas fa-copy"></i>
                                    </a>
                                </p>
                            </div>

                            <?php if($category->description): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Description</label>
                                    <p class="mb-0"><?php echo e($category->description); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if($category->parent): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Parent Category</label>
                                    <p class="mb-0">
                                        <a href="<?php echo e(route('admin.categories.show', $category->parent)); ?>" class="text-decoration-none">
                                            <i class="fas fa-folder me-2"></i><?php echo e($category->parent->name); ?>

                                        </a>
                                    </p>
                                </div>
                            <?php endif; ?>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Display Order</label>
                                    <p class="mb-0"><?php echo e($category->sort_order); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Created</label>
                                    <p class="mb-0"><?php echo e($category->created_at->format('M d, Y h:i A')); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <?php if($category->image): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Category Image</label>
                                    <div>
                                        <img src="<?php echo e(Storage::url($category->image)); ?>" 
                                             alt="<?php echo e($category->name); ?>" 
                                             class="img-thumbnail" 
                                             style="max-width: 200px; max-height: 200px;">
                                    </div>
                                </div>
                            <?php elseif($category->icon): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Category Icon</label>
                                    <div>
                                        <i class="<?php echo e($category->icon); ?> fa-4x text-primary"></i>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SEO Information -->
            <?php if($category->meta_title || $category->meta_description || $category->meta_keywords): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            SEO Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if($category->meta_title): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Meta Title</label>
                                <p class="mb-0"><?php echo e($category->meta_title); ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if($category->meta_description): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Meta Description</label>
                                <p class="mb-0"><?php echo e($category->meta_description); ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if($category->meta_keywords): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Meta Keywords</label>
                                <p class="mb-0"><?php echo e($category->meta_keywords); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Subcategories -->
            <?php if($category->children->count() > 0): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sitemap me-2"></i>
                            Subcategories (<?php echo e($category->children->count()); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php $__currentLoopData = $category->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center">
                                                <?php if($child->image): ?>
                                                    <img src="<?php echo e(Storage::url($child->image)); ?>" 
                                                         alt="<?php echo e($child->name); ?>" 
                                                         class="rounded me-3" 
                                                         style="width: 48px; height: 48px; object-fit: cover;">
                                                <?php elseif($child->icon): ?>
                                                    <i class="<?php echo e($child->icon); ?> fa-2x text-primary me-3"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-folder fa-2x text-muted me-3"></i>
                                                <?php endif; ?>
                                                
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">
                                                        <a href="<?php echo e(route('admin.categories.show', $child)); ?>" class="text-decoration-none">
                                                            <?php echo e($child->name); ?>

                                                        </a>
                                                    </h6>
                                                    <small class="text-muted"><?php echo e($child->products->count()); ?> products</small>
                                                    <?php if(!$child->is_active): ?>
                                                        <span class="badge bg-secondary ms-2">Inactive</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Products in Category -->
            <?php if($category->products->count() > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-box me-2"></i>
                                Products in Category (<?php echo e($category->products->count()); ?>)
                            </h5>
                            <a href="<?php echo e(route('admin.products.index', ['category' => $category->id])); ?>" class="btn btn-sm btn-outline-primary">
                                View All Products
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>SKU</th>
                                        <th>Price</th>
                                        <th>Stock</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $category->products->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if($product->featured_image): ?>
                                                        <img src="<?php echo e(Storage::url($product->featured_image)); ?>" 
                                                             alt="<?php echo e($product->name); ?>" 
                                                             class="rounded me-3" 
                                                             style="width: 40px; height: 40px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-box text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <div>
                                                        <strong><?php echo e($product->name); ?></strong>
                                                        <?php if($product->short_description): ?>
                                                            <br><small class="text-muted"><?php echo e(Str::limit($product->short_description, 50)); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <code><?php echo e($product->sku); ?></code>
                                            </td>
                                            <td>
                                                <?php if($product->isOnSale()): ?>
                                                    <span class="text-decoration-line-through text-muted">$<?php echo e(number_format($product->price, 2)); ?></span>
                                                    <br><strong class="text-success">$<?php echo e(number_format($product->sale_price, 2)); ?></strong>
                                                <?php else: ?>
                                                    <strong>$<?php echo e(number_format($product->price, 2)); ?></strong>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($product->manage_stock): ?>
                                                    <span class="badge bg-<?php echo e($product->isInStock() ? 'success' : 'danger'); ?>">
                                                        <?php echo e($product->stock_quantity); ?>

                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-info"><?php echo e(ucwords(str_replace('_', ' ', $product->stock_status))); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($product->is_active): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                                <?php if($product->is_featured): ?>
                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.products.show', $product)); ?>" 
                                                       class="btn btn-sm btn-outline-info" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.products.edit', $product)); ?>" 
                                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Status & Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Status & Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-primary mb-1"><?php echo e($category->products->count()); ?></h4>
                                <small class="text-muted">Products</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-info mb-1"><?php echo e($category->children->count()); ?></h4>
                                <small class="text-muted">Subcategories</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Status</label>
                        <div>
                            <?php if($category->is_active): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Inactive</span>
                            <?php endif; ?>
                            
                            <?php if($category->is_featured): ?>
                                <span class="badge bg-warning ms-2">Featured</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Category Path</label>
                        <div class="small">
                            <?php $__currentLoopData = $category->path; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pathCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(!$loop->first): ?> › <?php endif; ?>
                                <a href="<?php echo e(route('admin.categories.show', $pathCategory)); ?>" class="text-decoration-none">
                                    <?php echo e($pathCategory->name); ?>

                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Last Updated</label>
                        <p class="mb-0 small"><?php echo e($category->updated_at->format('M d, Y h:i A')); ?></p>
                        <small class="text-muted"><?php echo e($category->updated_at->diffForHumans()); ?></small>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.categories.edit', $category)); ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            Edit Category
                        </a>
                        <a href="<?php echo e(route('admin.categories.create')); ?>?parent_id=<?php echo e($category->id); ?>" class="btn btn-outline-success">
                            <i class="fas fa-plus me-2"></i>
                            Add Subcategory
                        </a>
                        <a href="<?php echo e(route('admin.products.create')); ?>?category_id=<?php echo e($category->id); ?>" class="btn btn-outline-info">
                            <i class="fas fa-box me-2"></i>
                            Add Product
                        </a>
                        <?php if($category->products->count() > 0): ?>
                            <a href="<?php echo e(route('admin.products.index', ['category' => $category->id])); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-list me-2"></i>
                                View All Products
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Category Tree -->
            <?php if($category->parent || $category->children->count() > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sitemap me-2"></i>
                            Category Tree
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if($category->parent): ?>
                            <div class="mb-3">
                                <strong>Parent:</strong><br>
                                <a href="<?php echo e(route('admin.categories.show', $category->parent)); ?>" class="text-decoration-none">
                                    <i class="fas fa-level-up-alt me-2"></i><?php echo e($category->parent->name); ?>

                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if($category->children->count() > 0): ?>
                            <div>
                                <strong>Children:</strong><br>
                                <?php $__currentLoopData = $category->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="mt-1">
                                        <a href="<?php echo e(route('admin.categories.show', $child)); ?>" class="text-decoration-none">
                                            <i class="fas fa-folder me-2"></i><?php echo e($child->name); ?>

                                        </a>
                                        <small class="text-muted">(<?php echo e($child->products->count()); ?>)</small>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Delete Category
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the category <strong><?php echo e($category->name); ?></strong>?</p>
                    <?php if($category->products->count() > 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            This category contains <?php echo e($category->products->count()); ?> product(s). 
                            Please move or delete all products before deleting this category.
                        </div>
                    <?php endif; ?>
                    <?php if($category->children->count() > 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            This category has <?php echo e($category->children->count()); ?> subcategory(ies). 
                            Please delete all subcategories first.
                        </div>
                    <?php endif; ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <?php if($category->products->count() === 0 && $category->children->count() === 0): ?>
                        <form method="POST" action="<?php echo e(route('admin.categories.destroy', $category)); ?>" class="d-inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>
                                Delete Category
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function deleteCategory() {
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        Slug copied to clipboard!
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        });
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/admin/categories/show.blade.php ENDPATH**/ ?>