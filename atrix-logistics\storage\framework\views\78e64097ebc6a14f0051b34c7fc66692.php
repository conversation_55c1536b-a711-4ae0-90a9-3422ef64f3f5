<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>New Contact Form Submission</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        .footer {
            background-color: #6c757d;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 5px 5px;
            font-size: 12px;
        }
        .field {
            margin-bottom: 15px;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
        }
        .field-value {
            margin-top: 5px;
            padding: 10px;
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 3px;
        }
        .message-box {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 3px;
            margin-top: 15px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>New Contact Form Submission</h1>
        <p><?php echo e($siteSettings['site_name'] ?? config('app.name')); ?></p>
    </div>

    <div class="content">
        <p>You have received a new contact form submission from your website.</p>

        <div class="field">
            <div class="field-label">Name:</div>
            <div class="field-value"><?php echo e($contact->name); ?></div>
        </div>

        <div class="field">
            <div class="field-label">Email:</div>
            <div class="field-value">
                <a href="mailto:<?php echo e($contact->email); ?>"><?php echo e($contact->email); ?></a>
            </div>
        </div>

        <?php if($contact->phone): ?>
        <div class="field">
            <div class="field-label">Phone:</div>
            <div class="field-value">
                <a href="tel:<?php echo e($contact->phone); ?>"><?php echo e($contact->phone); ?></a>
            </div>
        </div>
        <?php endif; ?>

        <?php if($contact->subject): ?>
        <div class="field">
            <div class="field-label">Subject:</div>
            <div class="field-value"><?php echo e($contact->subject); ?></div>
        </div>
        <?php endif; ?>

        <div class="field">
            <div class="field-label">Message:</div>
            <div class="message-box"><?php echo e($contact->message); ?></div>
        </div>

        <div class="field">
            <div class="field-label">Submitted:</div>
            <div class="field-value"><?php echo e($contact->created_at->format('F j, Y \a\t g:i A')); ?></div>
        </div>

        <?php if($contact->ip_address): ?>
        <div class="field">
            <div class="field-label">IP Address:</div>
            <div class="field-value"><?php echo e($contact->ip_address); ?></div>
        </div>
        <?php endif; ?>

        <p>
            <a href="<?php echo e(route('admin.communications.contacts.show', $contact)); ?>" class="btn">
                View in Admin Dashboard
            </a>
        </p>

        <p>
            <strong>Quick Reply:</strong>
            <a href="mailto:<?php echo e($contact->email); ?>?subject=Re: <?php echo e($contact->subject ?: 'Your inquiry'); ?>">
                Reply to <?php echo e($contact->name); ?>

            </a>
        </p>
    </div>

    <div class="footer">
        <p>This email was sent automatically from <?php echo e($siteSettings['site_name'] ?? config('app.name')); ?> contact form.</p>
        <p>Contact ID: #<?php echo e($contact->id); ?></p>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/emails/contact-notification.blade.php ENDPATH**/ ?>