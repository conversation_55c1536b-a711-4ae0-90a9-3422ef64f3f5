<?php $__env->startSection('title', 'About Us - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics')); ?>
<?php $__env->startSection('description', $siteSettings['about_description'] ?? 'Learn about Atrix Logistics - your trusted partner for global shipping, freight services, and supply chain solutions. Discover our 15+ years of experience in automotive parts, steel products, and container shipping.'); ?>
<?php $__env->startSection('keywords', 'about atrix logistics, logistics company, freight services, shipping company, supply chain management, automotive parts shipping, steel products logistics, container shipping, global freight solutions'); ?>

<?php $__env->startSection('content'); ?>

<!-- Hero Section -->
<?php if (isset($component)) { $__componentOriginala9d931d4f11b4d2850df99e991db1dca = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala9d931d4f11b4d2850df99e991db1dca = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-hero','data' => ['title' => ''.e($siteSettings['about_hero_title'] ?? 'About <span class=\'text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400\'>Atrix Logistics</span>').'','subtitle' => ''.e($siteSettings['about_hero_subtitle'] ?? 'Your trusted partner in global logistics solutions since 2008').'','description' => 'Discover our journey, values, and commitment to excellence in the logistics industry. We specialize in automotive parts, steel products, and container shipping worldwide.','breadcrumbs' => [
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'About Us']
    ],'gradient' => 'from-green-900 via-gray-800 to-blue-900','stats' => [
        ['number' => '500+', 'label' => 'Happy Clients'],
        ['number' => '50+', 'label' => 'Countries Served'],
        ['number' => '99.8%', 'label' => 'On-Time Delivery'],
        ['number' => '15+', 'label' => 'Years Experience']
    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => ''.e($siteSettings['about_hero_title'] ?? 'About <span class=\'text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400\'>Atrix Logistics</span>').'','subtitle' => ''.e($siteSettings['about_hero_subtitle'] ?? 'Your trusted partner in global logistics solutions since 2008').'','description' => 'Discover our journey, values, and commitment to excellence in the logistics industry. We specialize in automotive parts, steel products, and container shipping worldwide.','breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'About Us']
    ]),'gradient' => 'from-green-900 via-gray-800 to-blue-900','stats' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        ['number' => '500+', 'label' => 'Happy Clients'],
        ['number' => '50+', 'label' => 'Countries Served'],
        ['number' => '99.8%', 'label' => 'On-Time Delivery'],
        ['number' => '15+', 'label' => 'Years Experience']
    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $attributes = $__attributesOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $component = $__componentOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__componentOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>

<!-- Company Story Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <!-- Content -->
            <div class="space-y-8 animate-on-scroll">
                <div class="space-y-4">
                    <h2 class="text-3xl lg:text-5xl font-bold font-heading text-secondary-900">
                        <?php echo e($siteSettings['company_story_title'] ?? 'Our Story'); ?>

                    </h2>
                    <p class="text-xl text-secondary-600">
                        <?php echo e($siteSettings['company_story_subtitle'] ?? 'Building trust through reliable logistics solutions'); ?>

                    </p>
                </div>
                
                <div class="space-y-6 text-gray-700 leading-relaxed">
                    <p>
                        <?php echo e($siteSettings['company_story_p1'] ?? 'Founded in 2008 with a vision to revolutionize the logistics industry, Atrix Logistics has grown from a small local operation to a global network of trusted partners spanning over 50 countries. Our commitment to excellence and customer satisfaction has been the driving force behind our remarkable journey and continued success.'); ?>

                    </p>
                    <p>
                        <?php echo e($siteSettings['company_story_p2'] ?? 'We specialize in three core areas: automotive parts logistics, steel products transportation, and container shipping solutions. Our comprehensive approach helps businesses of all sizes thrive in today\'s competitive marketplace. From small automotive components to large steel structures, our team of experienced professionals works tirelessly to ensure your cargo reaches its destination safely, securely, and on time.'); ?>

                    </p>
                    <p>
                        <?php echo e($siteSettings['company_story_p3'] ?? 'With cutting-edge technology, real-time tracking systems, and a customer-first approach, we continue to set new standards in the logistics industry. Our advanced warehouse management systems, AI-powered route optimization, and 24/7 customer support ensure that every shipment receives the attention it deserves while maintaining our core values of integrity, reliability, and innovation.'); ?>

                    </p>
                    <div class="flex flex-wrap gap-4 pt-4">
                        <div class="flex items-center space-x-2 text-green-600">
                            <i class="fas fa-check-circle"></i>
                            <span class="font-medium">ISO 9001:2015 Certified</span>
                        </div>
                        <div class="flex items-center space-x-2 text-green-600">
                            <i class="fas fa-check-circle"></i>
                            <span class="font-medium">24/7 Customer Support</span>
                        </div>
                        <div class="flex items-center space-x-2 text-green-600">
                            <i class="fas fa-check-circle"></i>
                            <span class="font-medium">Real-time Tracking</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Image -->
            <div class="animate-on-scroll">
                <?php if(isset($siteSettings['company_story_image']) && $siteSettings['company_story_image']): ?>
                    <img src="<?php echo e(Storage::url($siteSettings['company_story_image'])); ?>" alt="Our Story" class="w-full h-auto rounded-2xl shadow-xl">
                <?php else: ?>
                    <div class="bg-secondary-100 rounded-2xl p-8 h-96 flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-building text-6xl text-primary-600 mb-4"></i>
                            <h3 class="text-2xl font-bold text-secondary-900">Our Company</h3>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Mission, Vision, Values -->
<section class="py-20 bg-secondary-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-3xl lg:text-5xl font-bold font-heading text-secondary-900 mb-6">
                <?php echo e($siteSettings['mvv_section_title'] ?? 'Our Mission, Vision & Values'); ?>

            </h2>
            <p class="text-xl text-secondary-600 max-w-3xl mx-auto">
                <?php echo e($siteSettings['mvv_section_description'] ?? 'The principles that guide everything we do'); ?>

            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Mission -->
            <div class="bg-white rounded-2xl p-8 shadow-lg text-center card-hover animate-on-scroll">
                <div class="bg-primary-100 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-bullseye text-3xl text-primary-600"></i>
                </div>
                <h3 class="text-2xl font-bold text-secondary-900 mb-4">Our Mission</h3>
                <p class="text-gray-600 leading-relaxed">
                    <?php echo e($siteSettings['company_mission'] ?? 'To provide reliable, efficient, and cost-effective logistics solutions that enable our clients to focus on their core business while we handle their shipping needs with precision and care. We are committed to delivering exceptional service that exceeds expectations and builds lasting partnerships.'); ?>

                </p>
            </div>
            
            <!-- Vision -->
            <div class="bg-white rounded-2xl p-8 shadow-lg text-center card-hover animate-on-scroll">
                <div class="bg-primary-100 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-eye text-3xl text-primary-600"></i>
                </div>
                <h3 class="text-2xl font-bold text-secondary-900 mb-4">Our Vision</h3>
                <p class="text-gray-600 leading-relaxed">
                    <?php echo e($siteSettings['company_vision'] ?? 'To be the world\'s most trusted logistics partner, connecting businesses globally through innovative solutions and exceptional service that drives economic growth and prosperity. We envision a future where seamless logistics empowers businesses to reach new markets and achieve their full potential.'); ?>

                </p>
            </div>
            
            <!-- Values -->
            <div class="bg-white rounded-2xl p-8 shadow-lg text-center card-hover animate-on-scroll">
                <div class="bg-primary-100 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-heart text-3xl text-primary-600"></i>
                </div>
                <h3 class="text-2xl font-bold text-secondary-900 mb-4">Our Values</h3>
                <p class="text-gray-600 leading-relaxed">
                    <?php echo e($siteSettings['company_values'] ?? 'Integrity, reliability, innovation, and customer satisfaction are at the core of everything we do. We believe in building long-term partnerships based on trust, transparency, and mutual success. Our values guide every decision and ensure we deliver on our promises.'); ?>

                </p>
            </div>
        </div>
    </div>
</section>

<!-- Team Section -->
<?php if($teamMembers && $teamMembers->count() > 0): ?>
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-3xl lg:text-5xl font-bold font-heading text-secondary-900 mb-6">
                <?php echo e($siteSettings['team_section_title'] ?? 'Meet Our Team'); ?>

            </h2>
            <p class="text-xl text-secondary-600 max-w-3xl mx-auto">
                <?php echo e($siteSettings['team_section_description'] ?? 'The dedicated professionals behind our success'); ?>

            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            <?php $__currentLoopData = $teamMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden card-hover animate-on-scroll">
                <div class="h-64 bg-secondary-200 flex items-center justify-center">
                    <?php if($member->photo): ?>
                        <img src="<?php echo e(asset($member->photo)); ?>" alt="<?php echo e($member->name); ?>" class="w-full h-full object-cover">
                    <?php else: ?>
                        <i class="fas fa-user text-4xl text-secondary-400"></i>
                    <?php endif; ?>
                </div>
                <div class="p-6 text-center">
                    <h3 class="text-xl font-bold text-secondary-900 mb-2"><?php echo e($member->name); ?></h3>
                    <p class="text-primary-600 font-medium mb-2"><?php echo e($member->position); ?></p>
                    <?php if($member->department): ?>
                        <p class="text-secondary-500 text-sm mb-4"><?php echo e($member->department); ?></p>
                    <?php endif; ?>
                    <?php if($member->bio): ?>
                        <p class="text-secondary-600 text-sm"><?php echo e(Str::limit($member->bio, 100)); ?></p>
                    <?php endif; ?>
                    
                    <?php if($member->social_links && is_array($member->social_links)): ?>
                        <div class="flex justify-center space-x-3 mt-4">
                            <?php $__currentLoopData = $member->social_links; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $platform => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($url): ?>
                                    <a href="<?php echo e($url); ?>" target="_blank" class="text-secondary-400 hover:text-primary-600 transition-colors">
                                        <i class="fab fa-<?php echo e($platform); ?>"></i>
                                    </a>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Company Stats -->
<section class="py-20 relative overflow-hidden">
    <!-- Animated Gradient Background -->
    <div class="absolute inset-0 bg-gradient-to-br from-green-600 via-blue-600 to-purple-600 animate-gradient-xy"></div>

    <!-- Floating Particles Background -->
    <div class="absolute inset-0" id="particles-background"></div>

    <!-- Content -->
    <div class="container mx-auto px-4 relative z-10">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-3xl lg:text-5xl font-bold font-heading mb-6 text-white">
                <?php echo e($siteSettings['stats_section_title'] ?? 'Our Achievements'); ?>

            </h2>
            <p class="text-xl text-white text-opacity-90 max-w-3xl mx-auto">
                <?php echo e($siteSettings['stats_section_description'] ?? 'Numbers that speak for our commitment to excellence'); ?>

            </p>
        </div>

        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center animate-on-scroll">
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2">
                    <div class="text-4xl lg:text-6xl font-bold mb-2 text-white"><?php echo e($siteSettings['stat_shipments'] ?? '10K+'); ?></div>
                    <div class="text-white text-opacity-90 font-medium">Successful Shipments</div>
                </div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2">
                    <div class="text-4xl lg:text-6xl font-bold mb-2 text-white"><?php echo e($siteSettings['stat_countries'] ?? '50+'); ?></div>
                    <div class="text-white text-opacity-90 font-medium">Countries Served</div>
                </div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2">
                    <div class="text-4xl lg:text-6xl font-bold mb-2 text-white"><?php echo e($siteSettings['stat_clients'] ?? '500+'); ?></div>
                    <div class="text-white text-opacity-90 font-medium">Happy Clients</div>
                </div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2">
                    <div class="text-4xl lg:text-6xl font-bold mb-2 text-white"><?php echo e($siteSettings['stat_experience'] ?? '15+'); ?></div>
                    <div class="text-white text-opacity-90 font-medium">Years Experience</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Shipping Calculator Section -->
<section id="shipping-calculator" class="py-20 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23000000" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-3xl lg:text-5xl font-bold font-heading text-gray-900 mb-6">
                Shipping <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600">Calculator</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Get instant shipping estimates for your cargo. Calculate costs for domestic and international shipments with our advanced pricing tool.
            </p>
        </div>

        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden animate-on-scroll">
                <div class="bg-gradient-to-r from-green-600 to-blue-600 p-6">
                    <h3 class="text-2xl font-bold text-white text-center">
                        <i class="fas fa-calculator mr-3"></i>
                        Calculate Your Shipping Cost
                    </h3>
                </div>

                <form id="shippingCalculator" class="p-8 space-y-6">
                    <!-- Origin and Destination -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-map-marker-alt mr-2 text-green-600"></i>
                                Ship From
                            </label>
                            <input type="text" id="origin" name="origin" placeholder="City, State, Country"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-map-marker-alt mr-2 text-blue-600"></i>
                                Ship To
                            </label>
                            <input type="text" id="destination" name="destination" placeholder="City, State, Country"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>

                    <!-- Package Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Weight (kg)</label>
                            <input type="number" id="weight" name="weight" placeholder="0.0" step="0.1" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Length (cm)</label>
                            <input type="number" id="length" name="length" placeholder="0" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Width (cm)</label>
                            <input type="number" id="width" name="width" placeholder="0" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Height (cm)</label>
                            <input type="number" id="height" name="height" placeholder="0" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                    </div>

                    <!-- Service Type and Package Type -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Service Type</label>
                            <select id="serviceType" name="serviceType"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                <option value="">Select Service</option>
                                <option value="express">Express (1-2 days)</option>
                                <option value="standard">Standard (3-5 days)</option>
                                <option value="economy">Economy (5-7 days)</option>
                                <option value="freight">Freight (7-14 days)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Package Type</label>
                            <select id="packageType" name="packageType"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                <option value="">Select Package Type</option>
                                <option value="document">Document</option>
                                <option value="package">Package</option>
                                <option value="automotive">Automotive Parts</option>
                                <option value="steel">Steel Products</option>
                                <option value="container">Container</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Declared Value and Special Services -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Declared Value (USD)</label>
                            <input type="number" id="declaredValue" name="declaredValue" placeholder="0.00" step="0.01" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Special Services</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" name="insurance" class="rounded text-green-600 focus:ring-green-500">
                                    <span class="ml-2 text-sm text-gray-700">Insurance</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="signature" class="rounded text-green-600 focus:ring-green-500">
                                    <span class="ml-2 text-sm text-gray-700">Signature Required</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Calculate Button -->
                    <div class="text-center pt-6">
                        <button type="submit" class="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:-translate-y-1 shadow-lg hover:shadow-xl">
                            <i class="fas fa-calculator mr-2"></i>
                            Calculate Shipping Cost
                        </button>
                    </div>

                    <!-- Results Section -->
                    <div id="calculatorResults" class="hidden mt-8 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-chart-line mr-2 text-green-600"></i>
                            Estimated Shipping Costs
                        </h4>
                        <div id="resultsContent" class="space-y-3">
                            <!-- Results will be populated here -->
                        </div>
                        <div class="mt-6 text-center">
                            <button onclick="openQuoteModal()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                Get Detailed Quote
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4 text-center">
        <div class="max-w-4xl mx-auto space-y-8 animate-on-scroll">
            <h2 class="text-3xl lg:text-5xl font-bold font-heading text-gray-900">
                <?php echo e($siteSettings['about_cta_title'] ?? 'Ready to Work with Us?'); ?>

            </h2>
            <p class="text-xl text-gray-600">
                <?php echo e($siteSettings['about_cta_description'] ?? 'Join hundreds of satisfied clients who trust us with their logistics needs.'); ?>

            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="openQuoteModal()" class="bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 btn-primary">
                    <i class="fas fa-calculator mr-2"></i>
                    Get Free Quote
                </button>
                <a href="<?php echo e(route('contact')); ?>" class="border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 text-center">
                    <i class="fas fa-envelope mr-2"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('head'); ?>
<!-- Structured Data for SEO -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "<?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>",
    "description": "<?php echo e($siteSettings['site_description'] ?? 'Professional logistics and freight solutions provider specializing in global shipping, warehousing, and supply chain management.'); ?>",
    "url": "<?php echo e(route('home')); ?>",
    "logo": "<?php echo e(isset($siteSettings['site_logo']) ? Storage::url($siteSettings['site_logo']) : ''); ?>",
    "foundingDate": "2008",
    "numberOfEmployees": "50-100",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "<?php echo e($siteSettings['company_country'] ?? 'United States'); ?>",
        "addressLocality": "<?php echo e($siteSettings['company_city'] ?? ''); ?>",
        "addressRegion": "<?php echo e($siteSettings['company_state'] ?? ''); ?>",
        "postalCode": "<?php echo e($siteSettings['company_postal_code'] ?? ''); ?>",
        "streetAddress": "<?php echo e($siteSettings['company_address'] ?? ''); ?>"
    },
    "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "<?php echo e($siteSettings['company_phone'] ?? ''); ?>",
        "contactType": "customer service",
        "email": "<?php echo e($siteSettings['company_email'] ?? ''); ?>"
    },
    "sameAs": [
        "<?php echo e($siteSettings['social_facebook'] ?? ''); ?>",
        "<?php echo e($siteSettings['social_twitter'] ?? ''); ?>",
        "<?php echo e($siteSettings['social_linkedin'] ?? ''); ?>",
        "<?php echo e($siteSettings['social_instagram'] ?? ''); ?>"
    ],
    "serviceArea": {
        "@type": "GeoCircle",
        "geoMidpoint": {
            "@type": "GeoCoordinates",
            "latitude": "<?php echo e($siteSettings['company_latitude'] ?? '40.7128'); ?>",
            "longitude": "<?php echo e($siteSettings['company_longitude'] ?? '-74.0060'); ?>"
        },
        "geoRadius": "20000000"
    },
    "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Logistics Services",
        "itemListElement": [
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Automotive Parts Shipping",
                    "description": "Specialized logistics solutions for automotive parts and components"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Steel Products Transportation",
                    "description": "Heavy-duty transportation solutions for steel products and materials"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Container Shipping",
                    "description": "Full container load and less than container load shipping services"
                }
            }
        ]
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Animated Gradient Background */
@keyframes gradient-xy {
    0%, 100% {
        background-size: 400% 400%;
        background-position: left center;
    }
    50% {
        background-size: 200% 200%;
        background-position: right center;
    }
}

.animate-gradient-xy {
    animation: gradient-xy 15s ease infinite;
}

/* Particles Animation */
.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

.particle:nth-child(1) { animation-delay: 0s; }
.particle:nth-child(2) { animation-delay: 0.5s; }
.particle:nth-child(3) { animation-delay: 1s; }
.particle:nth-child(4) { animation-delay: 1.5s; }
.particle:nth-child(5) { animation-delay: 2s; }
.particle:nth-child(6) { animation-delay: 2.5s; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle hash navigation on page load
    if (window.location.hash === '#shipping-calculator') {
        setTimeout(() => {
            const calculatorSection = document.getElementById('shipping-calculator');
            if (calculatorSection) {
                calculatorSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }, 500); // Small delay to ensure page is fully loaded
    }

    // Initialize particles background
    initParticlesBackground();

    // Initialize shipping calculator
    const calculatorForm = document.getElementById('shippingCalculator');
    const resultsDiv = document.getElementById('calculatorResults');
    const resultsContent = document.getElementById('resultsContent');

    // Get dynamic currency from localization service
    const currentCurrency = '<?php echo e(app(\App\Services\SeoLocalizationService::class)->getCurrencyForLocale()); ?>';
    const currencySymbols = {
        'USD': '$', 'EUR': '€', 'GBP': '£', 'CAD': 'C$', 'AUD': 'A$',
        'JPY': '¥', 'CNY': '¥', 'CHF': 'CHF', 'SEK': 'kr', 'NOK': 'kr',
        'DKK': 'kr', 'PLN': 'zł', 'CZK': 'Kč', 'HUF': 'Ft', 'RUB': '₽',
        'BRL': 'R$', 'MXN': '$', 'INR': '₹', 'KRW': '₩', 'SGD': 'S$',
        'HKD': 'HK$', 'NZD': 'NZ$', 'ZAR': 'R'
    };
    const currencySymbol = currencySymbols[currentCurrency] || '$';

    // Update currency label in form
    const currencyLabel = document.querySelector('label[for="declaredValue"]');
    if (currencyLabel) {
        currencyLabel.innerHTML = `<i class="fas fa-dollar-sign mr-2 text-green-600"></i>Declared Value (${currentCurrency})`;
    }

    calculatorForm.addEventListener('submit', function(e) {
        e.preventDefault();
        calculateShipping();
    });

    function initParticlesBackground() {
        const particlesContainer = document.getElementById('particles-background');
        if (!particlesContainer) return;

        // Create floating particles
        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';

            // Random size between 4px and 12px
            const size = Math.random() * 8 + 4;
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';

            // Random position
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';

            // Random animation duration
            particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
            particle.style.animationDelay = Math.random() * 2 + 's';

            particlesContainer.appendChild(particle);
        }
    }

    function calculateShipping() {
        // Get form data
        const formData = new FormData(calculatorForm);
        const data = Object.fromEntries(formData);

        // Basic validation
        if (!data.origin || !data.destination || !data.weight) {
            alert('Please fill in origin, destination, and weight fields.');
            return;
        }

        // Show loading state
        const submitBtn = calculatorForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Calculating...';
        submitBtn.disabled = true;

        // Simulate API call with realistic shipping calculations
        setTimeout(() => {
            const estimates = generateShippingEstimates(data);
            displayResults(estimates);

            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

            // Show results
            resultsDiv.classList.remove('hidden');
            resultsDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }, 2000);
    }

    function generateShippingEstimates(data) {
        const weight = parseFloat(data.weight) || 1;
        const length = parseFloat(data.length) || 10;
        const width = parseFloat(data.width) || 10;
        const height = parseFloat(data.height) || 10;
        const declaredValue = parseFloat(data.declaredValue) || 100;

        // Calculate dimensional weight
        const dimensionalWeight = (length * width * height) / 5000; // Standard divisor
        const chargeableWeight = Math.max(weight, dimensionalWeight);

        // Base rates per kg (from admin settings)
        const baseRates = {
            express: <?php echo e($siteSettings['shipping_base_rate_express'] ?? '15.50'); ?>,
            standard: <?php echo e($siteSettings['shipping_base_rate_standard'] ?? '8.75'); ?>,
            economy: <?php echo e($siteSettings['shipping_base_rate_economy'] ?? '5.25'); ?>,
            freight: <?php echo e($siteSettings['shipping_base_rate_freight'] ?? '3.80'); ?>

        };

        // Distance multiplier (simulated based on origin/destination)
        const isInternational = data.origin.toLowerCase().includes('country') || data.destination.toLowerCase().includes('country');
        const distanceMultiplier = isInternational ? <?php echo e($siteSettings['shipping_international_multiplier'] ?? '2.5'); ?> : 1.2;

        // Package type multiplier
        const packageMultipliers = {
            document: 0.8,
            package: 1.0,
            automotive: 1.3,
            steel: 1.5,
            container: 2.0,
            other: 1.1
        };

        const packageMultiplier = packageMultipliers[data.packageType] || 1.0;

        // Calculate estimates
        const estimates = [];

        if (data.serviceType) {
            // Single service calculation
            const baseRate = baseRates[data.serviceType] || baseRates.standard;
            const cost = chargeableWeight * baseRate * distanceMultiplier * packageMultiplier;

            estimates.push({
                service: data.serviceType.charAt(0).toUpperCase() + data.serviceType.slice(1),
                cost: cost,
                transit: getTransitTime(data.serviceType, isInternational),
                icon: getServiceIcon(data.serviceType)
            });
        } else {
            // Multiple service options
            Object.keys(baseRates).forEach(service => {
                const baseRate = baseRates[service];
                const cost = chargeableWeight * baseRate * distanceMultiplier * packageMultiplier;

                estimates.push({
                    service: service.charAt(0).toUpperCase() + service.slice(1),
                    cost: cost,
                    transit: getTransitTime(service, isInternational),
                    icon: getServiceIcon(service)
                });
            });
        }

        // Add insurance cost if selected
        if (data.insurance) {
            estimates.forEach(estimate => {
                estimate.cost += declaredValue * <?php echo e($siteSettings['shipping_insurance_rate'] ?? '0.02'); ?>; // % of declared value
            });
        }

        // Add signature cost if selected
        if (data.signature) {
            estimates.forEach(estimate => {
                estimate.cost += <?php echo e($siteSettings['shipping_signature_fee'] ?? '5.00'); ?>; // Flat fee for signature
            });
        }

        return estimates.sort((a, b) => a.cost - b.cost);
    }

    function getTransitTime(service, isInternational) {
        const times = {
            express: isInternational ? '1-3 business days' : '1-2 business days',
            standard: isInternational ? '3-7 business days' : '3-5 business days',
            economy: isInternational ? '7-14 business days' : '5-7 business days',
            freight: isInternational ? '14-21 business days' : '7-14 business days'
        };
        return times[service] || '3-5 business days';
    }

    function getServiceIcon(service) {
        const icons = {
            express: 'fas fa-bolt text-red-500',
            standard: 'fas fa-truck text-blue-500',
            economy: 'fas fa-clock text-green-500',
            freight: 'fas fa-ship text-purple-500'
        };
        return icons[service] || 'fas fa-box text-gray-500';
    }

    function displayResults(estimates) {
        resultsContent.innerHTML = '';

        estimates.forEach((estimate, index) => {
            const resultItem = document.createElement('div');
            resultItem.className = 'flex justify-between items-center p-4 bg-white rounded-lg shadow-sm border border-gray-200';
            resultItem.innerHTML = `
                <div class="flex items-center space-x-3">
                    <i class="${estimate.icon}"></i>
                    <div>
                        <div class="font-semibold text-gray-900">${estimate.service} Service</div>
                        <div class="text-sm text-gray-600">${estimate.transit}</div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-green-600">${currencySymbol}${estimate.cost.toFixed(2)}</div>
                    <div class="text-sm text-gray-500">${currentCurrency}</div>
                </div>
            `;
            resultsContent.appendChild(resultItem);
        });

        // Add disclaimer
        const disclaimer = document.createElement('div');
        disclaimer.className = 'mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg';
        disclaimer.innerHTML = `
            <div class="flex items-start space-x-2">
                <i class="fas fa-info-circle text-yellow-600 mt-0.5"></i>
                <div class="text-sm text-yellow-800">
                    <strong>Note:</strong> These are estimated costs for planning purposes.
                    Final pricing may vary based on actual package dimensions, destination zones,
                    fuel surcharges, and additional services. Contact us for accurate quotes.
                </div>
            </div>
        `;
        resultsContent.appendChild(disclaimer);
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/frontend/about.blade.php ENDPATH**/ ?>