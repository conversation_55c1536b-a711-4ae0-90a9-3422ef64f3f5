<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Quote;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class QuoteController extends Controller
{
    /**
     * Display a listing of quotes
     */
    public function index(Request $request): View
    {
        $query = Quote::with(['user', 'assignedTo']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('quote_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('service_type')) {
            $query->where('service_type', $request->service_type);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $quotes = $query->latest()->paginate(20);

        // Get filter options
        $statuses = [
            'pending' => 'Pending',
            'reviewing' => 'Reviewing',
            'quoted' => 'Quoted',
            'accepted' => 'Accepted',
            'rejected' => 'Rejected',
            'expired' => 'Expired',
            'converted' => 'Converted',
        ];

        $serviceTypes = [
            'domestic_shipping' => 'Domestic Shipping',
            'international_shipping' => 'International Shipping',
            'express_delivery' => 'Express Delivery',
            'freight_shipping' => 'Freight Shipping',
            'warehousing' => 'Warehousing',
            'custom_logistics' => 'Custom Logistics',
            'bulk_shipping' => 'Bulk Shipping',
            'specialized_transport' => 'Specialized Transport',
        ];

        $priorities = [
            'standard' => 'Standard',
            'urgent' => 'Urgent',
            'express' => 'Express',
        ];

        $admins = User::where('role', 'admin')->get(['id', 'name']);

        // Quote statistics
        $stats = [
            'total_quotes' => Quote::count(),
            'pending_quotes' => Quote::where('status', 'pending')->count(),
            'active_quotes' => Quote::whereIn('status', ['pending', 'reviewing', 'quoted'])->count(),
            'accepted_quotes' => Quote::where('status', 'accepted')->count(),
            'total_value' => Quote::where('status', 'accepted')->sum('final_price'),
            'avg_quote_value' => Quote::where('status', 'accepted')->avg('final_price') ?? 0,
        ];

        return view('admin.quotes.index', compact(
            'quotes', 'statuses', 'serviceTypes', 'priorities', 'admins', 'stats'
        ));
    }

    /**
     * Show the form for creating a new quote
     */
    public function create(): View
    {
        $customers = User::where('role', 'customer')->get(['id', 'name', 'email']);
        $admins = User::where('role', 'admin')->get(['id', 'name']);

        $serviceTypes = [
            'domestic_shipping' => 'Domestic Shipping',
            'international_shipping' => 'International Shipping',
            'express_delivery' => 'Express Delivery',
            'freight_shipping' => 'Freight Shipping',
            'warehousing' => 'Warehousing',
            'custom_logistics' => 'Custom Logistics',
            'bulk_shipping' => 'Bulk Shipping',
            'specialized_transport' => 'Specialized Transport',
        ];

        $priorities = [
            'standard' => 'Standard',
            'urgent' => 'Urgent',
            'express' => 'Express',
        ];

        return view('admin.quotes.create', compact('customers', 'admins', 'serviceTypes', 'priorities'));
    }

    /**
     * Store a newly created quote
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'service_type' => 'required|in:domestic_shipping,international_shipping,express_delivery,freight_shipping,warehousing,custom_logistics,bulk_shipping,specialized_transport',
            'priority' => 'required|in:standard,urgent,express',
            'description' => 'required|string|max:5000',
            'origin_address' => 'required|string|max:500',
            'origin_city' => 'required|string|max:100',
            'origin_state' => 'nullable|string|max:100',
            'origin_postal_code' => 'nullable|string|max:20',
            'origin_country' => 'required|string|max:100',
            'destination_address' => 'required|string|max:500',
            'destination_city' => 'required|string|max:100',
            'destination_state' => 'nullable|string|max:100',
            'destination_postal_code' => 'nullable|string|max:20',
            'destination_country' => 'required|string|max:100',
            'package_count' => 'required|integer|min:1',
            'total_weight' => 'nullable|numeric|min:0',
            'weight_unit' => 'required|in:kg,lbs',
            'package_type' => 'nullable|string|max:100',
            'package_description' => 'nullable|string|max:1000',
            'fragile' => 'boolean',
            'hazardous' => 'boolean',
            'declared_value' => 'nullable|numeric|min:0',
            'preferred_pickup_date' => 'nullable|date|after_or_equal:today',
            'required_delivery_date' => 'nullable|date|after_or_equal:preferred_pickup_date',
            'delivery_speed' => 'required|in:standard,express,overnight,same_day',
            'insurance_required' => 'boolean',
            'signature_required' => 'boolean',
            'assigned_to' => 'nullable|exists:users,id',
            'admin_notes' => 'nullable|string|max:2000',
        ]);

        // Handle dimensions
        if ($request->filled(['length', 'width', 'height'])) {
            $validated['dimensions'] = [
                'length' => $request->length,
                'width' => $request->width,
                'height' => $request->height,
                'unit' => $request->dimension_unit ?? 'cm',
            ];
        }

        // Handle additional services
        if ($request->filled('additional_services')) {
            $validated['additional_services'] = $request->additional_services;
        }

        // Set defaults
        $validated['fragile'] = $request->has('fragile');
        $validated['hazardous'] = $request->has('hazardous');
        $validated['insurance_required'] = $request->has('insurance_required');
        $validated['signature_required'] = $request->has('signature_required');
        $validated['status'] = 'pending';

        $quote = Quote::create($validated);

        return redirect()->route('admin.quotes.show', $quote)
                        ->with('success', 'Quote created successfully.');
    }

    /**
     * Display the specified quote
     */
    public function show(Quote $quote): View
    {
        $quote->load(['user', 'assignedTo']);

        return view('admin.quotes.show', compact('quote'));
    }

    /**
     * Show the form for editing the specified quote
     */
    public function edit(Quote $quote): View
    {
        $customers = User::where('role', 'customer')->get(['id', 'name', 'email']);
        $admins = User::where('role', 'admin')->get(['id', 'name']);

        $serviceTypes = [
            'domestic_shipping' => 'Domestic Shipping',
            'international_shipping' => 'International Shipping',
            'express_delivery' => 'Express Delivery',
            'freight_shipping' => 'Freight Shipping',
            'warehousing' => 'Warehousing',
            'custom_logistics' => 'Custom Logistics',
            'bulk_shipping' => 'Bulk Shipping',
            'specialized_transport' => 'Specialized Transport',
        ];

        $priorities = [
            'standard' => 'Standard',
            'urgent' => 'Urgent',
            'express' => 'Express',
        ];

        return view('admin.quotes.edit', compact('quote', 'customers', 'admins', 'serviceTypes', 'priorities'));
    }

    /**
     * Update the specified quote
     */
    public function update(Request $request, Quote $quote): RedirectResponse
    {
        $validated = $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'service_type' => 'required|in:domestic_shipping,international_shipping,express_delivery,freight_shipping,warehousing,custom_logistics,bulk_shipping,specialized_transport',
            'priority' => 'required|in:standard,urgent,express',
            'description' => 'required|string|max:5000',
            'origin_address' => 'required|string|max:500',
            'origin_city' => 'required|string|max:100',
            'origin_state' => 'nullable|string|max:100',
            'origin_postal_code' => 'nullable|string|max:20',
            'origin_country' => 'required|string|max:100',
            'destination_address' => 'required|string|max:500',
            'destination_city' => 'required|string|max:100',
            'destination_state' => 'nullable|string|max:100',
            'destination_postal_code' => 'nullable|string|max:20',
            'destination_country' => 'required|string|max:100',
            'package_count' => 'required|integer|min:1',
            'total_weight' => 'nullable|numeric|min:0',
            'weight_unit' => 'required|in:kg,lbs',
            'package_type' => 'nullable|string|max:100',
            'package_description' => 'nullable|string|max:1000',
            'fragile' => 'boolean',
            'hazardous' => 'boolean',
            'declared_value' => 'nullable|numeric|min:0',
            'preferred_pickup_date' => 'nullable|date|after_or_equal:today',
            'required_delivery_date' => 'nullable|date|after_or_equal:preferred_pickup_date',
            'delivery_speed' => 'required|in:standard,express,overnight,same_day',
            'insurance_required' => 'boolean',
            'signature_required' => 'boolean',
            'assigned_to' => 'nullable|exists:users,id',
            'admin_notes' => 'nullable|string|max:2000',
        ]);

        // Handle dimensions
        if ($request->filled(['length', 'width', 'height'])) {
            $validated['dimensions'] = [
                'length' => $request->length,
                'width' => $request->width,
                'height' => $request->height,
                'unit' => $request->dimension_unit ?? 'cm',
            ];
        }

        // Handle additional services
        if ($request->filled('additional_services')) {
            $validated['additional_services'] = $request->additional_services;
        }

        // Set boolean values
        $validated['fragile'] = $request->has('fragile');
        $validated['hazardous'] = $request->has('hazardous');
        $validated['insurance_required'] = $request->has('insurance_required');
        $validated['signature_required'] = $request->has('signature_required');

        $quote->update($validated);

        return redirect()->route('admin.quotes.show', $quote)
                        ->with('success', 'Quote updated successfully.');
    }

    /**
     * Remove the specified quote
     */
    public function destroy(Quote $quote): RedirectResponse
    {
        $quote->delete();

        return redirect()->route('admin.quotes.index')
                        ->with('success', 'Quote deleted successfully.');
    }

    /**
     * Provide a quote with pricing
     */
    public function provideQuote(Request $request, Quote $quote): JsonResponse
    {
        $request->validate([
            'quoted_price' => 'required|numeric|min:0',
            'pricing_breakdown' => 'nullable|string|max:2000',
            'discount_amount' => 'nullable|numeric|min:0',
            'expires_at' => 'required|date|after:now',
            'admin_notes' => 'nullable|string|max:2000',
        ]);

        $finalPrice = $request->quoted_price - ($request->discount_amount ?? 0);

        $quote->update([
            'status' => 'quoted',
            'quoted_price' => $request->quoted_price,
            'pricing_breakdown' => $request->pricing_breakdown,
            'discount_amount' => $request->discount_amount ?? 0,
            'final_price' => $finalPrice,
            'expires_at' => $request->expires_at,
            'quoted_at' => now(),
            'admin_notes' => $request->admin_notes,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Quote provided successfully.',
            'quote' => $quote->fresh(),
        ]);
    }

    /**
     * Update quote status
     */
    public function updateStatus(Request $request, Quote $quote): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:pending,reviewing,quoted,accepted,rejected,expired,converted',
            'notes' => 'nullable|string|max:2000',
        ]);

        $updateData = ['status' => $request->status];

        // Set timestamps based on status
        switch ($request->status) {
            case 'accepted':
                $updateData['accepted_at'] = now();
                break;
            case 'rejected':
                $updateData['rejected_at'] = now();
                break;
        }

        if ($request->filled('notes')) {
            $updateData['admin_notes'] = $request->notes;
        }

        $quote->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Quote status updated successfully.',
            'status' => $quote->formatted_status,
        ]);
    }

    /**
     * Assign quote to admin
     */
    public function assign(Request $request, Quote $quote): JsonResponse
    {
        $request->validate([
            'assigned_to' => 'required|exists:users,id',
        ]);

        $quote->update(['assigned_to' => $request->assigned_to]);

        return response()->json([
            'success' => true,
            'message' => 'Quote assigned successfully.',
            'assigned_to' => $quote->assignedTo->name ?? 'Unassigned',
        ]);
    }

    /**
     * Bulk actions for quotes
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'quote_ids' => 'required|array',
            'quote_ids.*' => 'exists:quotes,id',
            'action' => 'required|in:assign,update_status,delete',
            'assigned_to' => 'required_if:action,assign|exists:users,id',
            'status' => 'required_if:action,update_status|in:pending,reviewing,quoted,accepted,rejected,expired,converted',
        ]);

        $quotes = Quote::whereIn('id', $request->quote_ids)->get();
        $count = 0;

        foreach ($quotes as $quote) {
            switch ($request->action) {
                case 'assign':
                    $quote->update(['assigned_to' => $request->assigned_to]);
                    $count++;
                    break;
                case 'update_status':
                    $quote->update(['status' => $request->status]);
                    $count++;
                    break;
                case 'delete':
                    $quote->delete();
                    $count++;
                    break;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully processed {$count} quotes.",
        ]);
    }
}
